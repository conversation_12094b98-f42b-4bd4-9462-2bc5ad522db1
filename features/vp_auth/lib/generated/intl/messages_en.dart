// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "account_account": MessageLookupByLibrary.simpleMessage("Account"),
    "account_account_holder_name": MessageLookupByLibrary.simpleMessage(
      "Account Holder Name",
    ),
    "account_account_number": MessageLookupByLibrary.simpleMessage(
      "Account Number",
    ),
    "account_active_fingerprint": MessageLookupByLibrary.simpleMessage(
      "Please sign in to the app to activate with your fingerprint/faceid",
    ),
    "account_agree": MessageLookupByLibrary.simpleMessage("Agree"),
    "account_auto_find_acc": MessageLookupByLibrary.simpleMessage(
      "Find account number automatically",
    ),
    "account_back": MessageLookupByLibrary.simpleMessage("Back"),
    "account_back_acc_name": MessageLookupByLibrary.simpleMessage(
      "Bank Account name (Unsigned)",
    ),
    "account_back_identi_card": MessageLookupByLibrary.simpleMessage(
      "Back of Identity card",
    ),
    "account_bank": MessageLookupByLibrary.simpleMessage("Bank"),
    "account_bank_branch": MessageLookupByLibrary.simpleMessage("Branch"),
    "account_bank_info": MessageLookupByLibrary.simpleMessage(
      "Bank information",
    ),
    "account_behind": MessageLookupByLibrary.simpleMessage("Behind"),
    "account_biometric_invalid": MessageLookupByLibrary.simpleMessage(
      "Biometric invalid",
    ),
    "account_birthday": MessageLookupByLibrary.simpleMessage("Birthday"),
    "account_cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "account_cancelChangePass": MessageLookupByLibrary.simpleMessage(
      "Hủy thay đổi mật khẩu đăng nhập",
    ),
    "account_cancelChangePassConfirm": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn muốn hủy các bước thay đổi mật khẩu?",
    ),
    "account_cancel_register": MessageLookupByLibrary.simpleMessage(
      "Cancel subscription",
    ),
    "account_cancel_register_content1": MessageLookupByLibrary.simpleMessage(
      "You did",
    ),
    "account_cancel_register_content2": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to cancel the subscription?",
    ),
    "account_cancel_register_title": MessageLookupByLibrary.simpleMessage(
      "Cancel account registration?",
    ),
    "account_capture_profile": MessageLookupByLibrary.simpleMessage(
      "Take a profile photo",
    ),
    "account_caring_staff": MessageLookupByLibrary.simpleMessage(
      "Caring staff",
    ),
    "account_cccd": MessageLookupByLibrary.simpleMessage("CCCD/HC/GPKD Number"),
    "account_change_pass_success": MessageLookupByLibrary.simpleMessage(
      "Change the password successfully",
    ),
    "account_change_pass_success_description": MessageLookupByLibrary.simpleMessage(
      "The password has been successfully changed. Please login again to use the application",
    ),
    "account_change_password": MessageLookupByLibrary.simpleMessage(
      "Change password",
    ),
    "account_change_pin": MessageLookupByLibrary.simpleMessage(
      "Change the PIN",
    ),
    "account_change_pin_for_continue": MessageLookupByLibrary.simpleMessage(
      "Please enter the PIN to continue",
    ),
    "account_change_pin_successfully": MessageLookupByLibrary.simpleMessage(
      "Change the battery successfully",
    ),
    "account_change_pin_successfully_des": MessageLookupByLibrary.simpleMessage(
      "Your battery has been successfully changed",
    ),
    "account_change_the_pin": MessageLookupByLibrary.simpleMessage(
      "Change PIN",
    ),
    "account_check_email_guide_content1": MessageLookupByLibrary.simpleMessage(
      "Receive transaction and asset alerts",
    ),
    "account_check_email_guide_content2": MessageLookupByLibrary.simpleMessage(
      "Get market information, investment recommendations",
    ),
    "account_check_email_guide_content3": MessageLookupByLibrary.simpleMessage(
      "Update new products and services, offers",
    ),
    "account_check_email_guide_content4": MessageLookupByLibrary.simpleMessage(
      "Enhance account security",
    ),
    "account_check_email_guide_title": MessageLookupByLibrary.simpleMessage(
      "Email is used to:",
    ),
    "account_close": MessageLookupByLibrary.simpleMessage("Close"),
    "account_confirm_info": MessageLookupByLibrary.simpleMessage(
      "Confirm information",
    ),
    "account_confirm_new_password": MessageLookupByLibrary.simpleMessage(
      "Confirm new password",
    ),
    "account_confirm_supporter": MessageLookupByLibrary.simpleMessage(
      "Confirm Caregiver information",
    ),
    "account_content_success": MessageLookupByLibrary.simpleMessage(
      "Please login with your account number and password sent via sms.\nThank you for trusting and accompanying VPBank Securities!",
    ),
    "account_content_success_combo1": MessageLookupByLibrary.simpleMessage(
      "Information about securities accounts and bank account opening status will be sent to you via registered sms & email.",
    ),
    "account_content_success_combo2": MessageLookupByLibrary.simpleMessage(
      "Thank you for trusting and investing with VPBank Securities!",
    ),
    "account_continue_text": MessageLookupByLibrary.simpleMessage("Continue"),
    "account_contract": MessageLookupByLibrary.simpleMessage("Contract"),
    "account_contract_and_term": MessageLookupByLibrary.simpleMessage(
      "Contract & Terms",
    ),
    "account_contract_failed": MessageLookupByLibrary.simpleMessage(
      "Load of contract failed. Please try again.",
    ),
    "account_contract_note1": MessageLookupByLibrary.simpleMessage(
      "By pressing",
    ),
    "account_contract_note2": MessageLookupByLibrary.simpleMessage("Confirm"),
    "account_contract_note3": MessageLookupByLibrary.simpleMessage(
      ", you confirm that you have read, understood and signed the Account Opening Agreement at VPBank Securities. Simultaneously agree for VPBankS to share customer\'s data and information and customer\'s securities trading account with VPBank Partners.",
    ),
    "account_contract_success": MessageLookupByLibrary.simpleMessage(
      "Contract download successful.",
    ),
    "account_counting_otp": MessageLookupByLibrary.simpleMessage(
      "OTP code has an expiry date in",
    ),
    "account_create_new_password": MessageLookupByLibrary.simpleMessage(
      "Create a new password",
    ),
    "account_create_over_otp": MessageLookupByLibrary.simpleMessage(
      "You have reached the daily OTP creation limit and exceeded the maximum attempts. Please try again tomorrow.",
    ),
    "account_create_password": MessageLookupByLibrary.simpleMessage(
      "Create password",
    ),
    "account_current_address": MessageLookupByLibrary.simpleMessage(
      "Current residential address",
    ),
    "account_current_password": MessageLookupByLibrary.simpleMessage(
      "Current password",
    ),
    "account_customer_confirmation": MessageLookupByLibrary.simpleMessage(
      "Customer confirmation:",
    ),
    "account_customer_confirmation1": MessageLookupByLibrary.simpleMessage(
      "Not a US citizen, do not hold a US Green Card, and not a resident in the US.",
    ),
    "account_customer_confirmation2": MessageLookupByLibrary.simpleMessage(
      "No other beneficial ownership.",
    ),
    "account_customer_confirmation3": MessageLookupByLibrary.simpleMessage(
      "No trustee involved.",
    ),
    "account_date_of_birth": MessageLookupByLibrary.simpleMessage(
      "Date of birth",
    ),
    "account_declare_information": MessageLookupByLibrary.simpleMessage(
      "Declare information",
    ),
    "account_depository_acc": MessageLookupByLibrary.simpleMessage(
      "Depository account number",
    ),
    "account_disable_fingerprint": MessageLookupByLibrary.simpleMessage(
      "Fingerprint login has been disabled due to incorrect input multiple times. Please use password to login the app",
    ),
    "account_do_not_contain_special_characters":
        MessageLookupByLibrary.simpleMessage(
          "Do not contain special characters",
        ),
    "account_document_invalid": MessageLookupByLibrary.simpleMessage(
      "Please register with your CCCD number to open both VPBank securities and banking accounts. Or click \'Continue\' to open a securities account with your ID card.",
    ),
    "account_does_not_contain_space": MessageLookupByLibrary.simpleMessage(
      "Does not contain space",
    ),
    "account_dont_have_support": MessageLookupByLibrary.simpleMessage(
      "I want to find a caregiver",
    ),
    "account_e_kyc_guide": MessageLookupByLibrary.simpleMessage(
      "VPBank Securities needs data about your face to ensure security and information verification.",
    ),
    "account_e_kyc_title": MessageLookupByLibrary.simpleMessage(
      "Face Recognition",
    ),
    "account_edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "account_edit_bank_acc": MessageLookupByLibrary.simpleMessage(
      "Edit bank accounts",
    ),
    "account_edit_info": MessageLookupByLibrary.simpleMessage(
      "Edit information",
    ),
    "account_edit_info_guide": MessageLookupByLibrary.simpleMessage(
      "You can manually edit the information. However, your account may be temporarily restricted until verification (3-5 days).",
    ),
    "account_edit_info_short": MessageLookupByLibrary.simpleMessage(
      "Edit info",
    ),
    "account_ekyc_description_1": MessageLookupByLibrary.simpleMessage(
      "Use the original and valid CCCD/ID card.",
    ),
    "account_ekyc_description_2": MessageLookupByLibrary.simpleMessage(
      "Place the document flat within the frame.",
    ),
    "account_ekyc_description_3": MessageLookupByLibrary.simpleMessage(
      "Make sure all information on the document is clear and\nreadable. Avoid dark, blurry, or glared photos.",
    ),
    "account_ekyc_description_4": MessageLookupByLibrary.simpleMessage(
      "VPBank Securities will also require facial data to ensure identity and security.",
    ),
    "account_ekyc_start": MessageLookupByLibrary.simpleMessage(
      "The system prepares to identify later",
    ),
    "account_ekyc_title_1": MessageLookupByLibrary.simpleMessage(
      "Have your CCCD/ID card ready. Make sure:",
    ),
    "account_email": MessageLookupByLibrary.simpleMessage("Email"),
    "account_email_register": MessageLookupByLibrary.simpleMessage(
      "<EMAIL>",
    ),
    "account_enter_bank_acc": MessageLookupByLibrary.simpleMessage(
      "Enter your bank account",
    ),
    "account_enter_email": MessageLookupByLibrary.simpleMessage(
      "Enter your email",
    ),
    "account_enter_pass": MessageLookupByLibrary.simpleMessage(
      "Enter password",
    ),
    "account_enter_password": MessageLookupByLibrary.simpleMessage(
      "Please enter a password",
    ),
    "account_enter_phone_number": MessageLookupByLibrary.simpleMessage(
      "Enter your phone number",
    ),
    "account_enter_the_new_password": MessageLookupByLibrary.simpleMessage(
      "Enter the new password",
    ),
    "account_enter_the_new_pin": MessageLookupByLibrary.simpleMessage(
      "Enter the new PIN",
    ),
    "account_enter_username": MessageLookupByLibrary.simpleMessage(
      "Please enter account number",
    ),
    "account_enter_your_password": MessageLookupByLibrary.simpleMessage(
      "Please enter your password",
    ),
    "account_enter_your_pin": MessageLookupByLibrary.simpleMessage(
      "Please enter your PIN",
    ),
    "account_error": MessageLookupByLibrary.simpleMessage(
      "An error occurred, please try again later!",
    ),
    "account_error_invalid_email": MessageLookupByLibrary.simpleMessage(
      "Invalid Email",
    ),
    "account_error_invalid_phone": MessageLookupByLibrary.simpleMessage(
      "Invalid account number",
    ),
    "account_exist_account_bank": MessageLookupByLibrary.simpleMessage(
      "You already have registration information at VPBank. Please click \'Understood\' to continue opening a securities account.",
    ),
    "account_expried_otp": MessageLookupByLibrary.simpleMessage(
      "The OTP has expired. Please try again.",
    ),
    "account_face_to_frame": MessageLookupByLibrary.simpleMessage(
      "Put faces in frame",
    ),
    "account_fail_enter_pass": MessageLookupByLibrary.simpleMessage(
      "Message \'Invalid password. A valid password must be at least 8 characters with at least 1 uppercase letter and 1 number\'",
    ),
    "account_find_branch_bank": MessageLookupByLibrary.simpleMessage(
      "Branch Search",
    ),
    "account_fingerprint": MessageLookupByLibrary.simpleMessage(
      "Scan your fingerprint (or face or whatever) to authenticate",
    ),
    "account_font_identi_card": MessageLookupByLibrary.simpleMessage(
      "Front of Identity card",
    ),
    "account_forgot_password": MessageLookupByLibrary.simpleMessage(
      "Forgot password",
    ),
    "account_forgot_the_pin": MessageLookupByLibrary.simpleMessage(
      "Forgot the PIN",
    ),
    "account_front": MessageLookupByLibrary.simpleMessage("Front"),
    "account_full_name": MessageLookupByLibrary.simpleMessage("Full name"),
    "account_gender": MessageLookupByLibrary.simpleMessage("Gender"),
    "account_gender_female": MessageLookupByLibrary.simpleMessage("Female"),
    "account_gender_male": MessageLookupByLibrary.simpleMessage("Male"),
    "account_go_to_settings_button": MessageLookupByLibrary.simpleMessage(
      "Setting",
    ),
    "account_go_to_settings_description": MessageLookupByLibrary.simpleMessage(
      "Biometric authentication is not set up on your device. Please either enable Touch ID or Face ID on your phone.",
    ),
    "account_goto_main_page": MessageLookupByLibrary.simpleMessage(
      "Go to home screen",
    ),
    "account_guide_edit_info": MessageLookupByLibrary.simpleMessage(
      "If your above information is not correct with the information on CCCD, you can correct your information or go to VPBank Securities counter for assistance",
    ),
    "account_guide_ekyc1": MessageLookupByLibrary.simpleMessage(
      "Rotate your face from the center of the camera to the sides to start recognizing faces",
    ),
    "account_guide_ekyc2": MessageLookupByLibrary.simpleMessage(
      "You note your eyes can always look at the camera. Do not rotate more than 90 degrees when you cannot look at the camera",
    ),
    "account_guide_input_email_or_phone": MessageLookupByLibrary.simpleMessage(
      "Please enter the OTP code that has been sent to your email or phone number",
    ),
    "account_guide_input_phone": MessageLookupByLibrary.simpleMessage(
      "The phone number is used to log in to the account and receive OTP in the system of VPBank Securities.",
    ),
    "account_have_support": MessageLookupByLibrary.simpleMessage(
      "I have a caregiver",
    ),
    "account_hint_email": MessageLookupByLibrary.simpleMessage("Email Address"),
    "account_hint_input_cccd": MessageLookupByLibrary.simpleMessage(
      "input CCCD",
    ),
    "account_hint_input_date_of_birth": MessageLookupByLibrary.simpleMessage(
      "Input date of birth",
    ),
    "account_hint_input_phone_or_email": MessageLookupByLibrary.simpleMessage(
      "input phone or email",
    ),
    "account_id_number": MessageLookupByLibrary.simpleMessage("CCCD/HC/GPKD"),
    "account_identify_card": MessageLookupByLibrary.simpleMessage("ID Card"),
    "account_if_not_user_reference": MessageLookupByLibrary.simpleMessage(
      "If you don\'t have a referral, you can leave it blank and move on",
    ),
    "account_incorrect_information": MessageLookupByLibrary.simpleMessage(
      "Incorrect information",
    ),
    "account_incorrect_information_message": MessageLookupByLibrary.simpleMessage(
      "The information you have just entered is incorrect, please try again.",
    ),
    "account_info_check_guide": MessageLookupByLibrary.simpleMessage(
      "Personal information",
    ),
    "account_info_email": MessageLookupByLibrary.simpleMessage(
      "Email information",
    ),
    "account_input_account": MessageLookupByLibrary.simpleMessage(
      "Account number 116C",
    ),
    "account_input_account_number": MessageLookupByLibrary.simpleMessage(
      "Input account number",
    ),
    "account_input_email_info": MessageLookupByLibrary.simpleMessage(
      "Import your email address",
    ),
    "account_input_email_or_phone": MessageLookupByLibrary.simpleMessage(
      "Email or phone number",
    ),
    "account_input_id_number": MessageLookupByLibrary.simpleMessage(
      "Enter CCCD number",
    ),
    "account_input_otp": MessageLookupByLibrary.simpleMessage("Input OTP"),
    "account_input_phone_info": MessageLookupByLibrary.simpleMessage(
      "Import your phone number",
    ),
    "account_input_phone_or_email": MessageLookupByLibrary.simpleMessage(
      "Phone or email",
    ),
    "account_input_user": MessageLookupByLibrary.simpleMessage(
      "Account number 116C",
    ),
    "account_intended_use": MessageLookupByLibrary.simpleMessage(
      "Purpose of using the account",
    ),
    "account_invalid_otp": MessageLookupByLibrary.simpleMessage(
      "OTP code is incorrect, please try again",
    ),
    "account_invalid_otp_account": MessageLookupByLibrary.simpleMessage(
      "OTP code is incorrect, please try again",
    ),
    "account_invalid_phone_or_email": MessageLookupByLibrary.simpleMessage(
      "Invalid phone or email",
    ),
    "account_invalid_username": MessageLookupByLibrary.simpleMessage(
      "Invalid username",
    ),
    "account_issued_date_card": MessageLookupByLibrary.simpleMessage(
      "Issued Date",
    ),
    "account_job": MessageLookupByLibrary.simpleMessage("Job"),
    "account_length_6_30_characters": MessageLookupByLibrary.simpleMessage(
      "Length 6 - 30 characters",
    ),
    "account_length_8_50_characters": MessageLookupByLibrary.simpleMessage(
      "Length 8 - 50 characters",
    ),
    "account_loading_wait": MessageLookupByLibrary.simpleMessage(
      "The system is processing, please wait...",
    ),
    "account_login": MessageLookupByLibrary.simpleMessage("Login"),
    "account_login_failde": MessageLookupByLibrary.simpleMessage(
      "Login failde",
    ),
    "account_market": MessageLookupByLibrary.simpleMessage("Market"),
    "account_match_face": MessageLookupByLibrary.simpleMessage(
      "Please fit your face into the frame",
    ),
    "account_message_no_touch_id": MessageLookupByLibrary.simpleMessage(
      "You have not set up your account\'s Touch ID on this device. Please login with password and set up Touch ID to use this function",
    ),
    "account_minimum_1_capital_letter": MessageLookupByLibrary.simpleMessage(
      "Minimum 1 capital letter",
    ),
    "account_minimum_1_character_number": MessageLookupByLibrary.simpleMessage(
      "Minimum 1 character number",
    ),
    "account_money_laundering_prevention": MessageLookupByLibrary.simpleMessage(
      "Money laundering prevention",
    ),
    "account_msg_one": MessageLookupByLibrary.simpleMessage(
      "Hiện tại VPBank chưa thể cung cấp dịch vụ cho bạn. Vui lòng liên hệ tổng đài chăm sóc khách hàng ********** để được hỗ trợ. Ấn “Đã hiểu” để trở về.",
    ),
    "account_new_password": MessageLookupByLibrary.simpleMessage(
      "New password",
    ),
    "account_new_pin": MessageLookupByLibrary.simpleMessage("New PIN code"),
    "account_next": MessageLookupByLibrary.simpleMessage("Next"),
    "account_no": MessageLookupByLibrary.simpleMessage("No"),
    "account_no_face_id": MessageLookupByLibrary.simpleMessage(
      "Face ID not installed",
    ),
    "account_no_need_support": MessageLookupByLibrary.simpleMessage(
      "I have no need",
    ),
    "account_no_result_search": MessageLookupByLibrary.simpleMessage(
      "No results were found",
    ),
    "account_no_support": MessageLookupByLibrary.simpleMessage(
      "Touch ID not supported",
    ),
    "account_no_touch_id": MessageLookupByLibrary.simpleMessage(
      "Touch ID not installed",
    ),
    "account_not_found_support": MessageLookupByLibrary.simpleMessage(
      "No caregiver found",
    ),
    "account_not_found_supporter_content": MessageLookupByLibrary.simpleMessage(
      "The care worker account number you just entered does not exist on the system. Please try again.",
    ),
    "account_not_found_supporter_title": MessageLookupByLibrary.simpleMessage(
      "Broker not found",
    ),
    "account_not_found_user_reference": MessageLookupByLibrary.simpleMessage(
      "No referrer found",
    ),
    "account_not_match_pass": MessageLookupByLibrary.simpleMessage(
      "The entered passwords do not match. Please try again.",
    ),
    "account_not_rotate_90": MessageLookupByLibrary.simpleMessage(
      "Do not rotate more than 90 degrees",
    ),
    "account_note": MessageLookupByLibrary.simpleMessage("Note"),
    "account_note_open_an_account_1": MessageLookupByLibrary.simpleMessage(
      "Currently, VPBank cannot provide services for you. Please contact the care hotline customer for support.",
    ),
    "account_note_open_an_account_2": MessageLookupByLibrary.simpleMessage(
      "Click \'Understood\' to continue opening a stock account.",
    ),
    "account_note_upload": MessageLookupByLibrary.simpleMessage(
      "Note:\\n- Please take photos of the same identity card\\n- Please take photos clearly, without losing angles and overexposing important information on the card",
    ),
    "account_notification": MessageLookupByLibrary.simpleMessage(
      "Notification",
    ),
    "account_ocr_fail_description": MessageLookupByLibrary.simpleMessage(
      "The photo you just took cannot be used. Please check if the information is blurry, in a well-lit environment and try again",
    ),
    "account_open_account": MessageLookupByLibrary.simpleMessage("Register"),
    "account_open_an_account": MessageLookupByLibrary.simpleMessage(
      "Open a VPBank bank account",
    ),
    "account_over_gen_otp": MessageLookupByLibrary.simpleMessage(
      "Exceeded daily OTP limit",
    ),
    "account_password": MessageLookupByLibrary.simpleMessage("Password"),
    "account_permanent_address": MessageLookupByLibrary.simpleMessage(
      "Permanent Address",
    ),
    "account_phone": MessageLookupByLibrary.simpleMessage("Phone"),
    "account_phone_number": MessageLookupByLibrary.simpleMessage(
      "Phone number",
    ),
    "account_phone_number_used": MessageLookupByLibrary.simpleMessage(
      "Your phone number is already linked to an account at VPBank. Please click \'Understood\' to proceed with opening a securities account.",
    ),
    "account_pin_is_sent": MessageLookupByLibrary.simpleMessage("PIN is sent"),
    "account_pin_not_match": MessageLookupByLibrary.simpleMessage(
      "PIN does not match each other. Please try again",
    ),
    "account_place_of_issued_card": MessageLookupByLibrary.simpleMessage(
      "Place Issued",
    ),
    "account_please_enter_your_password": MessageLookupByLibrary.simpleMessage(
      "Please enter your password",
    ),
    "account_please_input_email_or_phone": MessageLookupByLibrary.simpleMessage(
      "Please input email or phone number",
    ),
    "account_please_input_info_for_reset_password":
        MessageLookupByLibrary.simpleMessage(
          "Please enter the information below to reinstall the password",
        ),
    "account_please_input_user": MessageLookupByLibrary.simpleMessage(
      "Please input Account number 116C",
    ),
    "account_position": MessageLookupByLibrary.simpleMessage("Position"),
    "account_presenter": MessageLookupByLibrary.simpleMessage("Presenter"),
    "account_question_support": MessageLookupByLibrary.simpleMessage(
      "Don\'t have a caregiver yet?",
    ),
    "account_re_enter_otp": MessageLookupByLibrary.simpleMessage("Resend OTP"),
    "account_re_enter_pass": MessageLookupByLibrary.simpleMessage(
      "Re-enter password",
    ),
    "account_re_send_otp": MessageLookupByLibrary.simpleMessage("Resend OTP"),
    "account_register": MessageLookupByLibrary.simpleMessage(
      "Register account",
    ),
    "account_register_now": MessageLookupByLibrary.simpleMessage(
      "Register now",
    ),
    "account_register_success": MessageLookupByLibrary.simpleMessage(
      "CONGRATULATIONS ON SUCCESSFULLY OPENING A VPBANK SECURITIES ACCOUNT!",
    ),
    "account_register_success_combo": MessageLookupByLibrary.simpleMessage(
      "CONGRATULATIONS ON SUCCESSFULLY REGISTERING AND OPENING A COMBO ACCOUNT!",
    ),
    "account_requestReLogin": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đăng nhập lại để tiếp tục sử dụng dịch vụ",
    ),
    "account_retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "account_sdk_action": MessageLookupByLibrary.simpleMessage(
      "Open an account now",
    ),
    "account_sdk_vp_r0": MessageLookupByLibrary.simpleMessage(
      "Open a VPBank checking account now to enjoy the benefits",
    ),
    "account_sdk_vp_r1": MessageLookupByLibrary.simpleMessage(
      "Free to open beautiful Digital Account",
    ),
    "account_sdk_vp_r2": MessageLookupByLibrary.simpleMessage(
      "Quick deposit/withdrawal of securities",
    ),
    "account_sdk_vp_r3": MessageLookupByLibrary.simpleMessage(
      "Free Investment Advisory Service",
    ),
    "account_sdk_vp_r4": MessageLookupByLibrary.simpleMessage(
      "Unlimited Cashback with Credit Card",
    ),
    "account_sdk_vp_title": MessageLookupByLibrary.simpleMessage(
      "Dont have a VPBank bank account yet?",
    ),
    "account_search_bank": MessageLookupByLibrary.simpleMessage(
      "Search by bank name",
    ),
    "account_select_acc_number": MessageLookupByLibrary.simpleMessage(
      "Select securities account number",
    ),
    "account_select_acc_number_guide": MessageLookupByLibrary.simpleMessage(
      "Please select your stock trading account number",
    ),
    "account_select_account": MessageLookupByLibrary.simpleMessage(
      "Select account",
    ),
    "account_select_bank": MessageLookupByLibrary.simpleMessage(
      "Choose a bank",
    ),
    "account_sessionExpire": MessageLookupByLibrary.simpleMessage(
      "Phiên đăng nhập hết hạn",
    ),
    "account_sessionRegisterExprie": MessageLookupByLibrary.simpleMessage(
      "Phiên đăng ký hết hạn",
    ),
    "account_set_bond_enjoyment": MessageLookupByLibrary.simpleMessage(
      "Set bond enjoyment",
    ),
    "account_set_fund_enjoyment": MessageLookupByLibrary.simpleMessage(
      "Set fund enjoyment",
    ),
    "account_setup_pass": MessageLookupByLibrary.simpleMessage(
      "Set a password for your account",
    ),
    "account_sign_up_bank_note": MessageLookupByLibrary.simpleMessage(
      "The bank account holder information must match the information on your CCCD/ID card",
    ),
    "account_sign_up_bank_note2": MessageLookupByLibrary.simpleMessage(
      "This account also receives proceeds from the sale and dividends of fund certificates (if any).",
    ),
    "account_signature": MessageLookupByLibrary.simpleMessage("Signature"),
    "account_signature_description1": MessageLookupByLibrary.simpleMessage(
      "A photo of your signature will be used to verify and enhance account security.",
    ),
    "account_signature_description2": MessageLookupByLibrary.simpleMessage(
      "Please prepare your signature on a piece of paper so we can take a photo.",
    ),
    "account_signature_description_1": MessageLookupByLibrary.simpleMessage(
      "A photo of your signature will be used to\nverify and enhance account security.",
    ),
    "account_signature_description_2": MessageLookupByLibrary.simpleMessage(
      "Please prepare your signature on a piece of\npaper so we can take a photo.",
    ),
    "account_signature_title": MessageLookupByLibrary.simpleMessage(
      "Signature",
    ),
    "account_smartOTP": MessageLookupByLibrary.simpleMessage("Smart OTP"),
    "account_splash1_1": MessageLookupByLibrary.simpleMessage(
      "3 minutes eKYC\nAccount is available",
    ),
    "account_splash1_2": MessageLookupByLibrary.simpleMessage(
      "\nInstant transaction",
    ),
    "account_splash2": MessageLookupByLibrary.simpleMessage(
      "Only with\nPhone with camera\nand valid ID/CCCD",
    ),
    "account_start_ekyc": MessageLookupByLibrary.simpleMessage(
      "Start face recognition",
    ),
    "account_start_invest_vpbank": MessageLookupByLibrary.simpleMessage(
      "Start investing with VPBankS",
    ),
    "account_start_taking_photos": MessageLookupByLibrary.simpleMessage(
      "Start taking photos",
    ),
    "account_success": MessageLookupByLibrary.simpleMessage("Success"),
    "account_success_change_pass_pin": MessageLookupByLibrary.simpleMessage(
      "PIN and password created successfully",
    ),
    "account_success_change_password": MessageLookupByLibrary.simpleMessage(
      "Password changed successfully",
    ),
    "account_success_phone": MessageLookupByLibrary.simpleMessage(
      "Phone numbers that can be used",
    ),
    "account_suggest_acc_number": MessageLookupByLibrary.simpleMessage(
      "Suggestions for you",
    ),
    "account_support": MessageLookupByLibrary.simpleMessage("Support"),
    "account_support_call_hotline": MessageLookupByLibrary.simpleMessage(
      "Call Hotline",
    ),
    "account_support_website": MessageLookupByLibrary.simpleMessage("Website"),
    "account_support_zalo": MessageLookupByLibrary.simpleMessage(
      "Chat with support",
    ),
    "account_support_zalo2": MessageLookupByLibrary.simpleMessage(
      "VPBank Securities",
    ),
    "account_system_send_otp": MessageLookupByLibrary.simpleMessage(
      "The system has sent OTP to your phone number. Please enter the OTP received",
    ),
    "account_takePhotosSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Chụp ảnh thành công ",
    ),
    "account_take_a_photo_verification": MessageLookupByLibrary.simpleMessage(
      "Take a photo verification",
    ),
    "account_take_photo_id_card": MessageLookupByLibrary.simpleMessage(
      "Take CCCD",
    ),
    "account_take_photos_successfully": MessageLookupByLibrary.simpleMessage(
      "Take photos successfully",
    ),
    "account_tutorial_ocr": MessageLookupByLibrary.simpleMessage(
      "Make sure the card is captured clearly, the information is not blurred or glare",
    ),
    "account_tutorial_ocr2": MessageLookupByLibrary.simpleMessage(
      "Ensure captured information is clear, without blur or glare",
    ),
    "account_tutorial_ocr_1": MessageLookupByLibrary.simpleMessage(
      "Make sure the card is clearly captured and the information is not blurry or glared.",
    ),
    "account_tutorial_ocr_2": MessageLookupByLibrary.simpleMessage(
      "Ensure captured information is clear, without blur or glare.",
    ),
    "account_type_address": MessageLookupByLibrary.simpleMessage(
      "Enter address",
    ),
    "account_type_user_reference": MessageLookupByLibrary.simpleMessage(
      "Enter referrer code (if any)",
    ),
    "account_unrecognizable": MessageLookupByLibrary.simpleMessage(
      "Unrecognizable",
    ),
    "account_unregister_smart_otp_content": MessageLookupByLibrary.simpleMessage(
      "You have not registered Smart OTP on this device. Please log in and go to Security / Smart OTP to register.",
    ),
    "account_unregister_smart_otp_title": MessageLookupByLibrary.simpleMessage(
      "Smart OTP not registered",
    ),
    "account_update_cccd": MessageLookupByLibrary.simpleMessage(
      "Update citizen identification information",
    ),
    "account_upload_profile": MessageLookupByLibrary.simpleMessage(
      "Upload Profile",
    ),
    "account_us_citizen": MessageLookupByLibrary.simpleMessage("US Citizen"),
    "account_user_reference": MessageLookupByLibrary.simpleMessage("Presenter"),
    "account_valid_current_address": MessageLookupByLibrary.simpleMessage(
      "Current residential address must be at least 15 characters",
    ),
    "account_valid_id_card_empty": MessageLookupByLibrary.simpleMessage(
      "ID Card number cannot be left blank",
    ),
    "account_valid_id_card_special": MessageLookupByLibrary.simpleMessage(
      "ID Card number does not include letters, special characters and spaces",
    ),
    "account_valid_id_place": MessageLookupByLibrary.simpleMessage(
      "Place of issue cannot be left blank",
    ),
    "account_valid_id_wrong": MessageLookupByLibrary.simpleMessage(
      "Invalid ID number",
    ),
    "account_valid_name_empty": MessageLookupByLibrary.simpleMessage(
      "Full name must not be blank",
    ),
    "account_valid_name_wrong": MessageLookupByLibrary.simpleMessage(
      "Invalid full name. Full name must not include numbers or special characters",
    ),
    "account_valid_pass": MessageLookupByLibrary.simpleMessage(
      "Password must contain at least 8 characters, at least 1 capital letter and 1 number",
    ),
    "account_valid_per_address": MessageLookupByLibrary.simpleMessage(
      "Permanent address cannot be left blank",
    ),
    "account_verify": MessageLookupByLibrary.simpleMessage("Verify"),
    "account_verify_face_fail": MessageLookupByLibrary.simpleMessage(
      "Face detection failed, please try again",
    ),
    "account_verify_face_success": MessageLookupByLibrary.simpleMessage(
      "Face authentication successful",
    ),
    "account_vp_bank_account": MessageLookupByLibrary.simpleMessage(
      "Don\'t have VPBank account?",
    ),
    "account_wrong_back_id_card": MessageLookupByLibrary.simpleMessage(
      "Back side image is invalid. Please try again.",
    ),
    "account_wrong_front_id_card": MessageLookupByLibrary.simpleMessage(
      "Front side image is invalid. Please try again.",
    ),
    "account_wrong_otp": MessageLookupByLibrary.simpleMessage(
      "OTP code is incorrect, please try again",
    ),
    "account_wrong_phone": MessageLookupByLibrary.simpleMessage(
      "Invalid phone number. Phone number must start with 0 up to 10 numbers",
    ),
    "account_yes": MessageLookupByLibrary.simpleMessage("Yes"),
    "account_your_bank_info": MessageLookupByLibrary.simpleMessage(
      "Your bank account information",
    ),
    "account_your_signature": MessageLookupByLibrary.simpleMessage(
      "Your signature photo",
    ),
  };
}
