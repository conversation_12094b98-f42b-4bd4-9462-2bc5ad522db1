// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "account_account": MessageLookupByLibrary.simpleMessage("Tài khoản"),
    "account_account_holder_name": MessageLookupByLibrary.simpleMessage(
      "Tên chủ tài khoản",
    ),
    "account_account_number": MessageLookupByLibrary.simpleMessage(
      "Số tài khoản",
    ),
    "account_active_fingerprint": MessageLookupByLibrary.simpleMessage(
      "Quý khách vui lòng đăng nhập ứng dụng để kích hoạt bằng vân tay/khuôn mặt",
    ),
    "account_agree": MessageLookupByLibrary.simpleMessage("Đồng ý"),
    "account_auto_find_acc": MessageLookupByLibrary.simpleMessage(
      "Tìm số tài khoản tự động",
    ),
    "account_back": MessageLookupByLibrary.simpleMessage("Quay lại"),
    "account_back_acc_name": MessageLookupByLibrary.simpleMessage(
      "Họ tên chủ tài khoản (không dấu)",
    ),
    "account_back_identi_card": MessageLookupByLibrary.simpleMessage(
      "Mặt sau CCCD",
    ),
    "account_bank": MessageLookupByLibrary.simpleMessage("Ngân hàng"),
    "account_bank_branch": MessageLookupByLibrary.simpleMessage("Chi nhánh"),
    "account_bank_info": MessageLookupByLibrary.simpleMessage(
      "Thông tin ngân hàng",
    ),
    "account_behind": MessageLookupByLibrary.simpleMessage("Mặt sau"),
    "account_biometric_invalid": MessageLookupByLibrary.simpleMessage(
      "Xác thực sinh trắc học không hợp lệ",
    ),
    "account_birthday": MessageLookupByLibrary.simpleMessage("Ngày sinh"),
    "account_cancel": MessageLookupByLibrary.simpleMessage("Hủy"),
    "account_cancelChangePass": MessageLookupByLibrary.simpleMessage(
      "Hủy thay đổi mật khẩu đăng nhập",
    ),
    "account_cancelChangePassConfirm": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn muốn hủy các bước thay đổi mật khẩu?",
    ),
    "account_cancel_register": MessageLookupByLibrary.simpleMessage(
      "Hủy đăng ký",
    ),
    "account_cancel_register_content1": MessageLookupByLibrary.simpleMessage(
      "Bạn đã làm được",
    ),
    "account_cancel_register_content2": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn muốn hủy đăng ký?",
    ),
    "account_cancel_register_title": MessageLookupByLibrary.simpleMessage(
      "Hủy đăng ký tài khoản?",
    ),
    "account_capture_profile": MessageLookupByLibrary.simpleMessage(
      "Chụp ảnh hồ sơ",
    ),
    "account_caring_staff": MessageLookupByLibrary.simpleMessage(
      "Nhân viên chăm sóc",
    ),
    "account_cccd": MessageLookupByLibrary.simpleMessage("Số CCCD/HC/GPKD"),
    "account_change_pass_success": MessageLookupByLibrary.simpleMessage(
      "Thay đổi mật khẩu thành công",
    ),
    "account_change_pass_success_description": MessageLookupByLibrary.simpleMessage(
      "Mật khẩu đã được thay đổi thành công. Vui lòng đăng nhập lại để sử dụng ứng dụng",
    ),
    "account_change_password": MessageLookupByLibrary.simpleMessage(
      "Thay đổi mật khẩu",
    ),
    "account_change_pin": MessageLookupByLibrary.simpleMessage(
      "Thay đổi mã PIN",
    ),
    "account_change_pin_for_continue": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mã PIN để tiếp tục",
    ),
    "account_change_pin_successfully": MessageLookupByLibrary.simpleMessage(
      "Đổi PIN thành công",
    ),
    "account_change_pin_successfully_des": MessageLookupByLibrary.simpleMessage(
      "PIN của bạn đã được thay đổi thành công",
    ),
    "account_change_the_pin": MessageLookupByLibrary.simpleMessage(
      "Đổi mã PIN",
    ),
    "account_check_email_guide_content1": MessageLookupByLibrary.simpleMessage(
      "Nhận thông báo về giao dịch và tài sản",
    ),
    "account_check_email_guide_content2": MessageLookupByLibrary.simpleMessage(
      "Nhận thông tin thị trường, khuyến nghị đầu tư",
    ),
    "account_check_email_guide_content3": MessageLookupByLibrary.simpleMessage(
      "Cập nhật sản phẩm dịch vụ mới, ưu đãi",
    ),
    "account_check_email_guide_content4": MessageLookupByLibrary.simpleMessage(
      "Tăng cường bảo mật tài khoản",
    ),
    "account_check_email_guide_title": MessageLookupByLibrary.simpleMessage(
      "Email được sử dụng để:",
    ),
    "account_close": MessageLookupByLibrary.simpleMessage("Đóng"),
    "account_confirm_info": MessageLookupByLibrary.simpleMessage(
      "Xác nhận thông tin",
    ),
    "account_confirm_new_password": MessageLookupByLibrary.simpleMessage(
      "Xác nhận mật khẩu mới",
    ),
    "account_confirm_supporter": MessageLookupByLibrary.simpleMessage(
      "Xác nhận thông tin Nhân viên chăm sóc",
    ),
    "account_content_success": MessageLookupByLibrary.simpleMessage(
      "Thông tin về tài khoản chứng khoán sẽ được gửi đến Quý khách qua tin nhắn sms & email đã đăng ký.\nCảm ơn bạn đã tin tưởng và đồng hành đầu tư cùng Chứng khoán VPBank!",
    ),
    "account_content_success_combo1": MessageLookupByLibrary.simpleMessage(
      "Thông tin về tài khoản chứng khoán và trạng thái mở tài khoản ngân hàng sẽ được gửi đến Quý khách qua tin nhắn sms & email đã đăng ký.",
    ),
    "account_content_success_combo2": MessageLookupByLibrary.simpleMessage(
      "Cảm ơn bạn đã tin tưởng và đồng hành đầu tư cùng Chứng khoán VPBank!",
    ),
    "account_continue_text": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
    "account_contract": MessageLookupByLibrary.simpleMessage("Hợp đồng"),
    "account_contract_and_term": MessageLookupByLibrary.simpleMessage(
      "Hợp đồng & Điều khoản",
    ),
    "account_contract_failed": MessageLookupByLibrary.simpleMessage(
      "Tải hợp đồng không thành công. Vui lòng thử lại..",
    ),
    "account_contract_note1": MessageLookupByLibrary.simpleMessage(
      "Bằng việc chọn",
    ),
    "account_contract_note2": MessageLookupByLibrary.simpleMessage(
      "\"Xác nhận\"",
    ),
    "account_contract_note3": MessageLookupByLibrary.simpleMessage(
      ", bạn xác nhận đã đọc, hiểu và ký Hợp đồng Mở tài khoản tại Chứng khoán VPBank. Đồng thời đồng ý cho VPBankS chia sẻ dữ liệu, thông tin của KH và tài khoản giao dịch chứng khoán của KH cho đối tác VPBank.",
    ),
    "account_contract_success": MessageLookupByLibrary.simpleMessage(
      "Tải hợp đồng xuống thành công.",
    ),
    "account_counting_otp": MessageLookupByLibrary.simpleMessage(
      "Mã OTP có hạn sử dụng trong",
    ),
    "account_create_new_password": MessageLookupByLibrary.simpleMessage(
      "Tạo mới mật khẩu",
    ),
    "account_create_over_otp": MessageLookupByLibrary.simpleMessage(
      "Bạn đã đạt số lần tối đa tạo OTP/ngày và nhập sai hết số lần tối đa cho phép. Vui lòng thử lại ngày mai.",
    ),
    "account_create_password": MessageLookupByLibrary.simpleMessage(
      "Tạo mật khẩu",
    ),
    "account_current_address": MessageLookupByLibrary.simpleMessage(
      "Địa chỉ hiện tại",
    ),
    "account_current_password": MessageLookupByLibrary.simpleMessage(
      "Mật khẩu hiện tại",
    ),
    "account_customer_confirmation": MessageLookupByLibrary.simpleMessage(
      "Khách hàng xác nhận:",
    ),
    "account_customer_confirmation1": MessageLookupByLibrary.simpleMessage(
      "Không phải là công dân Hoa Kỳ, không có thẻ xanh, không phải là cá nhân cư trú tại Hoa Kỳ.",
    ),
    "account_customer_confirmation2": MessageLookupByLibrary.simpleMessage(
      "Không có sở hữu hưởng lợi khác.",
    ),
    "account_customer_confirmation3": MessageLookupByLibrary.simpleMessage(
      "Không có người nhận ủy thác.",
    ),
    "account_date_of_birth": MessageLookupByLibrary.simpleMessage(
      "Ngày tháng năm sinh/Ngày thành lập",
    ),
    "account_declare_information": MessageLookupByLibrary.simpleMessage(
      "Khai báo thông tin",
    ),
    "account_depository_acc": MessageLookupByLibrary.simpleMessage(
      "Số tài khoản lưu ký",
    ),
    "account_disable_fingerprint": MessageLookupByLibrary.simpleMessage(
      "Đăng nhập bằng vân tay đã bị vô hiệu hóa do nhập sai nhiều lần. Vui lòng sử dụng mật khẩu để đăng nhập ứng dụng.",
    ),
    "account_do_not_contain_special_characters":
        MessageLookupByLibrary.simpleMessage("Không chứa ký tự đặc biệt"),
    "account_document_invalid": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đăng ký bằng số CCCD để mở bộ tài khoản chứng khoán và ngân hàng VPBank. Hoặc nhấn \'Tiếp tục\' để mở tài khoản chứng khoán bằng CMT.",
    ),
    "account_does_not_contain_space": MessageLookupByLibrary.simpleMessage(
      "Không chứa khoảng trắng",
    ),
    "account_dont_have_support": MessageLookupByLibrary.simpleMessage(
      "Tôi muốn tìm nhân viên chăm sóc",
    ),
    "account_e_kyc_guide": MessageLookupByLibrary.simpleMessage(
      "Chứng khoán VPBank cần dữ liệu về khuôn mặt bạn để đảm bảo an toàn bảo mật.",
    ),
    "account_e_kyc_title": MessageLookupByLibrary.simpleMessage(
      "Nhận diện khuôn mặt",
    ),
    "account_edit": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
    "account_edit_bank_acc": MessageLookupByLibrary.simpleMessage(
      "Chỉnh sửa tài khoản ngân hàng",
    ),
    "account_edit_info": MessageLookupByLibrary.simpleMessage(
      "Chỉnh sửa thông tin",
    ),
    "account_edit_info_guide": MessageLookupByLibrary.simpleMessage(
      "Bạn có thể chỉnh sửa thông tin thủ công. Tuy nhiên, tài khoản của bạn có thể bị tạm thời hạn chế cho đến khi xác thực (3-5 ngày).",
    ),
    "account_edit_info_short": MessageLookupByLibrary.simpleMessage(
      "Chỉnh sửa",
    ),
    "account_ekyc_description_1": MessageLookupByLibrary.simpleMessage(
      "Sử dụng CCCD bản gốc và còn hiệu lực.",
    ),
    "account_ekyc_description_2": MessageLookupByLibrary.simpleMessage(
      "Đặt giấy tờ nằm thẳng trong khung hình.",
    ),
    "account_ekyc_description_3": MessageLookupByLibrary.simpleMessage(
      "Đảm bảo tất cả thông tin trên giấy tờ rõ ràng và\ncó thể đọc được. Tránh chụp tối, mờ, lóa sáng.",
    ),
    "account_ekyc_description_4": MessageLookupByLibrary.simpleMessage(
      "Chứng khoán VPBank cũng sẽ cần dữ liệu về khuôn mặt bạn để đảm bảo các vấn đề về thông tin và bảo mật.",
    ),
    "account_ekyc_start": MessageLookupByLibrary.simpleMessage(
      "Hệ thống chuẩn bị nhận diện sau",
    ),
    "account_ekyc_title_1": MessageLookupByLibrary.simpleMessage(
      "Chuẩn bị sẵn sàng CCCD của bạn. Hãy đảm bảo:",
    ),
    "account_email": MessageLookupByLibrary.simpleMessage("Email"),
    "account_email_register": MessageLookupByLibrary.simpleMessage(
      "<EMAIL>",
    ),
    "account_enter_bank_acc": MessageLookupByLibrary.simpleMessage(
      "Nhập tài khoản ngân hàng của bạn",
    ),
    "account_enter_email": MessageLookupByLibrary.simpleMessage(
      "Nhập email của bạn",
    ),
    "account_enter_pass": MessageLookupByLibrary.simpleMessage("Nhập mật khẩu"),
    "account_enter_password": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mật khẩu",
    ),
    "account_enter_phone_number": MessageLookupByLibrary.simpleMessage(
      "Nhập số điện thoại của bạn",
    ),
    "account_enter_the_new_password": MessageLookupByLibrary.simpleMessage(
      "Nhập lại mật khẩu mới",
    ),
    "account_enter_the_new_pin": MessageLookupByLibrary.simpleMessage(
      "Nhập lại mã PIN mới",
    ),
    "account_enter_username": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập số tài khoản",
    ),
    "account_enter_your_password": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mật khẩu của bạn",
    ),
    "account_enter_your_pin": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mã PIN",
    ),
    "account_error": MessageLookupByLibrary.simpleMessage(
      "Có lỗi xảy ra, vui lòng thử lại sau!",
    ),
    "account_error_invalid_email": MessageLookupByLibrary.simpleMessage(
      "Email không hợp lệ",
    ),
    "account_error_invalid_phone": MessageLookupByLibrary.simpleMessage(
      "Số tài khoản không hợp lệ",
    ),
    "account_exist_account_bank": MessageLookupByLibrary.simpleMessage(
      "Bạn đã có thông tin đăng ký tại VPBank. Vui lòng nhấn \'Đã hiểu\' để tiếp tục mở tài khoản chứng khoán.",
    ),
    "account_expried_otp": MessageLookupByLibrary.simpleMessage(
      "Mã OTP hết hạn. Vui lòng thử lại.",
    ),
    "account_face_to_frame": MessageLookupByLibrary.simpleMessage(
      "Đưa khuôn mặt vào trong khung hình",
    ),
    "account_fail_enter_pass": MessageLookupByLibrary.simpleMessage(
      "Thông báo \'Mật khẩu không hợp lệ. Mật khẩu hợp lệ phải tối thiểu 8 ký tự có ít nhất 1 chữ in hoa và 1 số\'",
    ),
    "account_find_branch_bank": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm chi nhánh",
    ),
    "account_fingerprint": MessageLookupByLibrary.simpleMessage(
      "Vui lòng sử dụng vân tay (hoặc khuôn mặt) để xác thực",
    ),
    "account_font_identi_card": MessageLookupByLibrary.simpleMessage(
      "Mặt trước CCCD",
    ),
    "account_forgot_password": MessageLookupByLibrary.simpleMessage(
      "Quên mật khẩu",
    ),
    "account_forgot_the_pin": MessageLookupByLibrary.simpleMessage(
      "Quên mã PIN",
    ),
    "account_front": MessageLookupByLibrary.simpleMessage("Mặt trước"),
    "account_full_name": MessageLookupByLibrary.simpleMessage("Họ và tên"),
    "account_gender": MessageLookupByLibrary.simpleMessage("Giới tính"),
    "account_gender_female": MessageLookupByLibrary.simpleMessage("Nữ"),
    "account_gender_male": MessageLookupByLibrary.simpleMessage("Nam"),
    "account_go_to_settings_button": MessageLookupByLibrary.simpleMessage(
      "Cài đặt",
    ),
    "account_go_to_settings_description": MessageLookupByLibrary.simpleMessage(
      "Xác thực sinh trắc học không được thiết lập trên thiết bị của bạn. Vui lòng bật Touch ID hoặc Face ID trên điện thoại của bạn.",
    ),
    "account_goto_main_page": MessageLookupByLibrary.simpleMessage(
      "Tới màn hình chính",
    ),
    "account_guide_edit_info": MessageLookupByLibrary.simpleMessage(
      "Nếu thông tin trên của bạn không chính xác với thông tin trên CCCD, bạn có thể chỉnh sửa hoặc ra quầy giao dịch VPBank Securities để hỗ trợ.",
    ),
    "account_guide_ekyc1": MessageLookupByLibrary.simpleMessage(
      "Xoay mặt bạn từ chính giữa camera sang 2 bên để có thể bắt đầu nhận diện khuôn mặt",
    ),
    "account_guide_ekyc2": MessageLookupByLibrary.simpleMessage(
      "Bạn lưu ý mắt bạn luôn có thể nhìn vào camera. Không quay quá 90 độ khi bạn không thể nhìn vào camera",
    ),
    "account_guide_input_email_or_phone": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mã OTP đã được gửi đến email hoặc số điện thoại của bạn",
    ),
    "account_guide_input_phone": MessageLookupByLibrary.simpleMessage(
      "Số điện thoại được dùng để đăng nhập vào tài khoản và nhận OTP trong hệ thống của VPBank Securities.",
    ),
    "account_have_support": MessageLookupByLibrary.simpleMessage(
      "Tôi có nhân viên chăm sóc",
    ),
    "account_hint_email": MessageLookupByLibrary.simpleMessage("Địa chỉ email"),
    "account_hint_input_cccd": MessageLookupByLibrary.simpleMessage(
      "Nhập số CCCD",
    ),
    "account_hint_input_date_of_birth": MessageLookupByLibrary.simpleMessage(
      "Nhập ngày tháng năm sinh",
    ),
    "account_hint_input_phone_or_email": MessageLookupByLibrary.simpleMessage(
      "Nhập số điện thoại hoặc email",
    ),
    "account_id_number": MessageLookupByLibrary.simpleMessage(
      "Số CCCD/HC/GPKD",
    ),
    "account_identify_card": MessageLookupByLibrary.simpleMessage("Số CCCD"),
    "account_if_not_user_reference": MessageLookupByLibrary.simpleMessage(
      "Nếu bạn không có người giới thiệu, bạn có thể bỏ trống và đi tiếp",
    ),
    "account_incorrect_information": MessageLookupByLibrary.simpleMessage(
      "Thông tin không chính xác",
    ),
    "account_incorrect_information_message":
        MessageLookupByLibrary.simpleMessage(
          "Thông tin bạn vừa nhập không chính xác, vui lòng thử lại.",
        ),
    "account_info_check_guide": MessageLookupByLibrary.simpleMessage(
      "Thông tin cá nhân",
    ),
    "account_info_email": MessageLookupByLibrary.simpleMessage(
      "Thông tin email",
    ),
    "account_input_account": MessageLookupByLibrary.simpleMessage(
      "Số tài khoản 116C hoặc 116F",
    ),
    "account_input_account_number": MessageLookupByLibrary.simpleMessage(
      "Nhập số tài khoản",
    ),
    "account_input_email_info": MessageLookupByLibrary.simpleMessage(
      "Nhập địa chỉ email của bạn",
    ),
    "account_input_id_number": MessageLookupByLibrary.simpleMessage(
      "Nhập số CCCD",
    ),
    "account_input_otp": MessageLookupByLibrary.simpleMessage("Nhập mã OTP"),
    "account_input_phone_info": MessageLookupByLibrary.simpleMessage(
      "Nhập số điện thoại của bạn",
    ),
    "account_input_phone_or_email": MessageLookupByLibrary.simpleMessage(
      "Số điện thoại hoặc email",
    ),
    "account_input_user": MessageLookupByLibrary.simpleMessage(
      "Số tài khoản 116C hoặc 116F",
    ),
    "account_invalid_otp": MessageLookupByLibrary.simpleMessage(
      "Mã OTP không chính xác, vui lòng thử lại",
    ),
    "account_invalid_otp_account": MessageLookupByLibrary.simpleMessage(
      "Mã OTP không chính xác. Vui lòng thử lại.",
    ),
    "account_invalid_phone_or_email": MessageLookupByLibrary.simpleMessage(
      "Email hoặc số điện thoại không hợp lệ",
    ),
    "account_invalid_username": MessageLookupByLibrary.simpleMessage(
      "Thông tin tài khoản không hợp lệ",
    ),
    "account_issued_date_card": MessageLookupByLibrary.simpleMessage(
      "Ngày cấp",
    ),
    "account_length_6_30_characters": MessageLookupByLibrary.simpleMessage(
      "Độ dài 6 - 30 ký tự",
    ),
    "account_length_8_50_characters": MessageLookupByLibrary.simpleMessage(
      "Độ dài 8 - 50 ký tự",
    ),
    "account_loading_wait": MessageLookupByLibrary.simpleMessage(
      "Hệ thống đang xử lý, vui lòng chờ...",
    ),
    "account_login": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
    "account_login_failde": MessageLookupByLibrary.simpleMessage(
      "Đăng nhập không thành công",
    ),
    "account_market": MessageLookupByLibrary.simpleMessage("Thị trường"),
    "account_message_no_touch_id": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa cài đặt Touch ID của tài khoản trên thiết bị này. Vui lòng đăng nhập bằng mật khẩu và thiết lập Touch ID để sử dụng chức năng này",
    ),
    "account_minimum_1_capital_letter": MessageLookupByLibrary.simpleMessage(
      "Tối thiểu 1 ký tự viết hoa",
    ),
    "account_minimum_1_character_number": MessageLookupByLibrary.simpleMessage(
      "Tối thiểu 1 ký tự số",
    ),
    "account_msg_one": MessageLookupByLibrary.simpleMessage(
      "Hiện tại VPBank chưa thể cung cấp dịch vụ cho bạn. Vui lòng liên hệ tổng đài chăm sóc khách hàng ********** để được hỗ trợ. Ấn “Đã hiểu” để trở về.",
    ),
    "account_new_password": MessageLookupByLibrary.simpleMessage(
      "Mật khẩu mới",
    ),
    "account_new_pin": MessageLookupByLibrary.simpleMessage("Mã PIN mới"),
    "account_next": MessageLookupByLibrary.simpleMessage("Tiếp theo"),
    "account_no": MessageLookupByLibrary.simpleMessage("Không"),
    "account_no_face_id": MessageLookupByLibrary.simpleMessage(
      "Face ID chưa được cài đặt",
    ),
    "account_no_need_support": MessageLookupByLibrary.simpleMessage(
      "Chưa có nhu cầu",
    ),
    "account_no_result_search": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy kết quả",
    ),
    "account_no_support": MessageLookupByLibrary.simpleMessage(
      "Không hỗ trợ Touch ID",
    ),
    "account_no_touch_id": MessageLookupByLibrary.simpleMessage(
      "Touch ID chưa được cài đặt",
    ),
    "account_not_found_support": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy nhân viên chăm sóc",
    ),
    "account_not_found_supporter_content": MessageLookupByLibrary.simpleMessage(
      "Số tài khoản nhân viên chăm sóc bạn vừa nhập không tồn tại trên hệ thống. Vui lòng thử lại.",
    ),
    "account_not_found_supporter_title": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy NVCS",
    ),
    "account_not_found_user_reference": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy người giới thiệu",
    ),
    "account_not_match_pass": MessageLookupByLibrary.simpleMessage(
      "Mật khẩu nhập không khớp nhau. Vui lòng thử lại.",
    ),
    "account_not_rotate_90": MessageLookupByLibrary.simpleMessage(
      "Không quay quá 90 độ",
    ),
    "account_note": MessageLookupByLibrary.simpleMessage("Lưu ý"),
    "account_note_open_an_account_1": MessageLookupByLibrary.simpleMessage(
      "Hiện tại VPBank chưa thể cung cấp dịch vụ cho bạn. Vui lòng liên hệ tổng đài chăm sóc khách hàng để được hỗ trợ.",
    ),
    "account_note_open_an_account_2": MessageLookupByLibrary.simpleMessage(
      "Ấn \'Đã hiểu\' để tiếp tục mở tài khoản chứng khoán.",
    ),
    "account_note_upload": MessageLookupByLibrary.simpleMessage(
      "Lưu ý:\\n• Vui lòng sử dụng cùng 1 loại giấy tờ để chụp ảnh Mặt trước và Mặt sau\\n• Vui lòng chụp rõ, không mất góc, không lóa sáng các thông tin quan trọng trên giấy tờ",
    ),
    "account_ocr_fail_description": MessageLookupByLibrary.simpleMessage(
      "Ảnh bạn vừa chụp không thể sử dụng. Vui lòng kiểm tra nếu các thông tin bị mờ, trong môi trường đủ ánh sáng và thử lại",
    ),
    "account_open_account": MessageLookupByLibrary.simpleMessage(
      "Mở tài khoản",
    ),
    "account_open_an_account": MessageLookupByLibrary.simpleMessage(
      "Mở tài khoản ngân hàng VPBank",
    ),
    "account_over_gen_otp": MessageLookupByLibrary.simpleMessage(
      "Quá số lần yêu cầu OTP trong ngày",
    ),
    "account_password": MessageLookupByLibrary.simpleMessage("Mật khẩu"),
    "account_permanent_address": MessageLookupByLibrary.simpleMessage(
      "Địa chỉ thường trú",
    ),
    "account_phone": MessageLookupByLibrary.simpleMessage("Số điện thoại"),
    "account_phone_number": MessageLookupByLibrary.simpleMessage(
      "Số điện thoại",
    ),
    "account_phone_number_used": MessageLookupByLibrary.simpleMessage(
      "Bạn đã có thông tin tài khoản tại ngân hàng VPBank. Vui lòng nhấn \'Đã hiểu\' để tiếp tục mở tài khoản chứng khoán.",
    ),
    "account_pin_is_sent": MessageLookupByLibrary.simpleMessage(
      "Mã PIN được gửi",
    ),
    "account_pin_not_match": MessageLookupByLibrary.simpleMessage(
      "Mã PIN không khớp nhau. Vui lòng thử lại",
    ),
    "account_place_of_issued_card": MessageLookupByLibrary.simpleMessage(
      "Nơi cấp",
    ),
    "account_please_enter_your_password": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mật khẩu của quý khách",
    ),
    "account_please_input_email_or_phone": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập Email hoặc số điện thoại",
    ),
    "account_please_input_info_for_reset_password":
        MessageLookupByLibrary.simpleMessage(
          "Vui lòng nhập thông tin dưới đây để cài đặt lại mật khẩu",
        ),
    "account_presenter": MessageLookupByLibrary.simpleMessage(
      "Người giới thiệu",
    ),
    "account_question_support": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa có nhân viên chăm sóc?",
    ),
    "account_re_enter_otp": MessageLookupByLibrary.simpleMessage("Gửi lại OTP"),
    "account_re_enter_pass": MessageLookupByLibrary.simpleMessage(
      "Nhập lại mật khẩu",
    ),
    "account_re_send_otp": MessageLookupByLibrary.simpleMessage("Gửi lại OTP"),
    "account_register": MessageLookupByLibrary.simpleMessage(
      "Đăng ký tài khoản",
    ),
    "account_register_now": MessageLookupByLibrary.simpleMessage(
      "Đăng ký ngay",
    ),
    "account_register_success": MessageLookupByLibrary.simpleMessage(
      "CHÚC MỪNG BẠN ĐÃ MỞ TÀI KHOẢN CHỨNG KHOÁN VPBANK THÀNH CÔNG!",
    ),
    "account_register_success_combo": MessageLookupByLibrary.simpleMessage(
      "CHÚC MỪNG BẠN ĐĂNG KÍ MỞ TÀI KHOẢN COMBO THÀNH CÔNG!",
    ),
    "account_requestReLogin": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đăng nhập lại để tiếp tục sử dụng dịch vụ",
    ),
    "account_retry": MessageLookupByLibrary.simpleMessage("Thử lại"),
    "account_sdk_action": MessageLookupByLibrary.simpleMessage(
      "Mở tài khoản ngay",
    ),
    "account_sdk_vp_r0": MessageLookupByLibrary.simpleMessage(
      "Mở Tài khoản thanh toán VPBank ngay để tận hưởng các ưu đãi",
    ),
    "account_sdk_vp_r1": MessageLookupByLibrary.simpleMessage(
      "Miễn phí mở Tài khoản số đẹp",
    ),
    "account_sdk_vp_r2": MessageLookupByLibrary.simpleMessage(
      "Nạp/rút tiền chứng khoán nhanh chóng",
    ),
    "account_sdk_vp_r3": MessageLookupByLibrary.simpleMessage(
      "Miễn phí Dịch vụ tư vấn đầu tư",
    ),
    "account_sdk_vp_r4": MessageLookupByLibrary.simpleMessage(
      "Hoàn tiền không giới hạn với Thẻ tín dụng",
    ),
    "account_sdk_vp_title": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa có tài khoản ngân hàng VPBank?",
    ),
    "account_search_bank": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm tên ngân hàng",
    ),
    "account_select_acc_number": MessageLookupByLibrary.simpleMessage(
      "Lựa chọn số tài khoản chứng khoán",
    ),
    "account_select_acc_number_guide": MessageLookupByLibrary.simpleMessage(
      "Hãy lựa chọn số tài khoản giao dịch chứng khoán của bạn",
    ),
    "account_select_account": MessageLookupByLibrary.simpleMessage(
      "Lựa chọn tài khoản",
    ),
    "account_select_bank": MessageLookupByLibrary.simpleMessage(
      "Chọn ngân hàng",
    ),
    "account_sessionExpire": MessageLookupByLibrary.simpleMessage(
      "Phiên đăng nhập hết hạn",
    ),
    "account_sessionRegisterExprie": MessageLookupByLibrary.simpleMessage(
      "Phiên đăng ký hết hạn",
    ),
    "account_set_bond_enjoyment": MessageLookupByLibrary.simpleMessage(
      "Đặt làm TK thụ hưởng trái phiếu",
    ),
    "account_set_fund_enjoyment": MessageLookupByLibrary.simpleMessage(
      "Đặt làm TK thụ hưởng chứng chỉ quỹ",
    ),
    "account_setup_pass": MessageLookupByLibrary.simpleMessage(
      "Cài đặt mật khẩu cho tài khoản của bạn",
    ),
    "account_sign_up_bank_note": MessageLookupByLibrary.simpleMessage(
      "Thông tin chủ tài khoản ngân hàng phải trùng khớp với thông tin trên CMND/CCCD của bạn",
    ),
    "account_sign_up_bank_note2": MessageLookupByLibrary.simpleMessage(
      "Tài khoản này đồng thời nhận tiền bán và cổ tức chứng chỉ quỹ (nếu có)",
    ),
    "account_signature": MessageLookupByLibrary.simpleMessage("Chữ ký"),
    "account_signature_description1": MessageLookupByLibrary.simpleMessage(
      "Ảnh chụp chữ ký của bạn sẽ được sử dụng để xác minh\n và tăng tính bảo mật tài khoản.",
    ),
    "account_signature_description2": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chuẩn bị chữ ký của bạn ra một tờ giấy\nđể chúng tôi có thể thực hiện chụp ảnh.",
    ),
    "account_signature_description_1": MessageLookupByLibrary.simpleMessage(
      "Ảnh chụp chữ ký của bạn sẽ được sử dụng để xác minh\nvà tăng tính bảo mật tài khoản.",
    ),
    "account_signature_description_2": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chuẩn bị chữ ký của bạn ra một tờ giấy\nđể chúng tôi có thể thực hiện chụp ảnh.",
    ),
    "account_signature_title": MessageLookupByLibrary.simpleMessage(
      "Chữ ký của bạn",
    ),
    "account_smartOTP": MessageLookupByLibrary.simpleMessage("Smart OTP"),
    "account_splash1_1": MessageLookupByLibrary.simpleMessage(
      "3 phút eKYC\nTài khoản có ngay",
    ),
    "account_splash1_2": MessageLookupByLibrary.simpleMessage(
      "\nGiao dịch liền tay",
    ),
    "account_splash2": MessageLookupByLibrary.simpleMessage(
      "Chỉ với\nĐiện thoại có camera\nvà CCCD còn hiệu lực",
    ),
    "account_start_ekyc": MessageLookupByLibrary.simpleMessage(
      "Bắt đầu nhận diện khuôn mặt",
    ),
    "account_start_invest_vpbank": MessageLookupByLibrary.simpleMessage(
      "Bắt đầu đầu tư cùng VPBankS",
    ),
    "account_start_taking_photos": MessageLookupByLibrary.simpleMessage(
      "Bắt đầu chụp ảnh",
    ),
    "account_success": MessageLookupByLibrary.simpleMessage("Thành công"),
    "account_success_change_pass_pin": MessageLookupByLibrary.simpleMessage(
      "Mã PIN và mật khẩu đã được tạo thành công",
    ),
    "account_success_change_password": MessageLookupByLibrary.simpleMessage(
      "Đổi mật khẩu thành công",
    ),
    "account_success_phone": MessageLookupByLibrary.simpleMessage(
      "Số điện thoại có thể được sử dụng",
    ),
    "account_suggest_acc_number": MessageLookupByLibrary.simpleMessage(
      "Các gợi ý dành cho bạn",
    ),
    "account_support": MessageLookupByLibrary.simpleMessage("Hỗ trợ"),
    "account_system_send_otp": MessageLookupByLibrary.simpleMessage(
      "Hệ thống đã gửi OTP vào số điện thoại của bạn. Vui lòng nhập mã OTP đã nhận",
    ),
    "account_takePhotosSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Chụp ảnh thành công ",
    ),
    "account_take_a_photo_verification": MessageLookupByLibrary.simpleMessage(
      "Chụp ảnh xác thực",
    ),
    "account_take_photo_id_card": MessageLookupByLibrary.simpleMessage(
      "Chụp CCCD",
    ),
    "account_take_photos_successfully": MessageLookupByLibrary.simpleMessage(
      "Chụp ảnh thành công",
    ),
    "account_tutorial_ocr": MessageLookupByLibrary.simpleMessage(
      "Đảm bảo thẻ được chụp rõ ràng, các thông tin không bị mờ hoặc lóa",
    ),
    "account_tutorial_ocr2": MessageLookupByLibrary.simpleMessage(
      "Đặt giấy tờ lên mặt phẳng, giữ giấy tờ nằm trong khung hình và đảm bảo thông tin hiển thị rõ ràng.",
    ),
    "account_tutorial_ocr_1": MessageLookupByLibrary.simpleMessage(
      "Đảm bảo thẻ được chụp rõ ràng, các thông tin không bị mờ hoặc lóa",
    ),
    "account_tutorial_ocr_2": MessageLookupByLibrary.simpleMessage(
      "Đặt giấy tờ lên mặt phẳng, giữ giấy tờ nằm trong khung hình và đảm bảo thông tin hiển thị rõ ràng.",
    ),
    "account_type_address": MessageLookupByLibrary.simpleMessage(
      "Nhập địa chỉ",
    ),
    "account_type_user_reference": MessageLookupByLibrary.simpleMessage(
      "Nhập mã người giới thiệu (nếu có)",
    ),
    "account_unrecognizable": MessageLookupByLibrary.simpleMessage(
      "Không thể nhận diện",
    ),
    "account_unregister_smart_otp_content": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa đăng ký Smart OTP trên thiết bị này. Vui lòng Đăng nhập và truy cập Bảo mật / Smart OTP để thực hiện đăng ký.",
    ),
    "account_unregister_smart_otp_title": MessageLookupByLibrary.simpleMessage(
      "Chưa đăng ký Smart OTP",
    ),
    "account_update_cccd": MessageLookupByLibrary.simpleMessage(
      "Cập nhật thông tin căn cước công dân",
    ),
    "account_upload_profile": MessageLookupByLibrary.simpleMessage("Tải hồ sơ"),
    "account_us_citizen": MessageLookupByLibrary.simpleMessage(
      "Công dân Hoa Kỳ",
    ),
    "account_user_reference": MessageLookupByLibrary.simpleMessage(
      "Người giới thiệu",
    ),
    "account_valid_current_address": MessageLookupByLibrary.simpleMessage(
      "Địa chỉ nơi ở hiện tại tối thiểu phải là 15 ký tự",
    ),
    "account_valid_id_card_empty": MessageLookupByLibrary.simpleMessage(
      "Số CCCD không được bỏ trống",
    ),
    "account_valid_id_card_special": MessageLookupByLibrary.simpleMessage(
      "Số CCCD không bao gồm chữ, ký tự đặc biệt và khoảng trắng",
    ),
    "account_valid_id_place": MessageLookupByLibrary.simpleMessage(
      "Nơi cấp không được bỏ trống",
    ),
    "account_valid_id_wrong": MessageLookupByLibrary.simpleMessage(
      "Số ID không hợp lệ",
    ),
    "account_valid_name_empty": MessageLookupByLibrary.simpleMessage(
      "Họ và tên không được bỏ trống",
    ),
    "account_valid_name_wrong": MessageLookupByLibrary.simpleMessage(
      "Họ và tên không hợp lệ. Họ và tên không bao gồm số và ký tự đặc biệt",
    ),
    "account_valid_pass": MessageLookupByLibrary.simpleMessage(
      "Mật khẩu hợp lệ phải tối thiểu 8 ký tự có ít nhất một chữ in hoa và một số.",
    ),
    "account_valid_per_address": MessageLookupByLibrary.simpleMessage(
      "Địa chỉ thường trú không được bỏ trống",
    ),
    "account_verify": MessageLookupByLibrary.simpleMessage("Xác thực"),
    "account_verify_face_fail": MessageLookupByLibrary.simpleMessage(
      "Nhận diện khuôn mặt không thành công, vui lòng thử lại",
    ),
    "account_verify_face_success": MessageLookupByLibrary.simpleMessage(
      "Xác thực khuôn mặt thành công",
    ),
    "account_vp_bank_account": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa có tài khoản VPBank?",
    ),
    "account_wrong_back_id_card": MessageLookupByLibrary.simpleMessage(
      "Ảnh chụp mặt sau không hợp lệ. Vui lòng thử lại.",
    ),
    "account_wrong_front_id_card": MessageLookupByLibrary.simpleMessage(
      "Ảnh chụp mặt trước không hợp lệ. Vui lòng thử lại.",
    ),
    "account_wrong_otp": MessageLookupByLibrary.simpleMessage(
      "Mã OTP không chính xác, vui lòng thử lại",
    ),
    "account_wrong_phone": MessageLookupByLibrary.simpleMessage(
      "Số điện thoại không hợp lệ. Số điện thoại phải bắt đầu bằng số 0 tối đa 10 số",
    ),
    "account_yes": MessageLookupByLibrary.simpleMessage("Có"),
    "account_your_bank_info": MessageLookupByLibrary.simpleMessage(
      "Thông tin tài khoản ngân hàng của bạn",
    ),
    "account_your_signature": MessageLookupByLibrary.simpleMessage(
      "Chụp chữ ký",
    ),
  };
}
