import 'package:json_annotation/json_annotation.dart';

part 'forgot_pass_auth_model.g.dart';

@JsonSerializable()
class ForgotPassAuthRequestModel {
  @JsonKey(name: 'phone_or_email')
  final String? value;
  @Json<PERSON>ey(name: 'idNumber')
  final String? idNumber;
  @Json<PERSON>ey(name: 'dob')
  final String? dob;
  @Json<PERSON>ey(name: 'otp')
  final String? otp;

  ForgotPassAuthRequestModel({this.value, this.otp, this.idNumber, this.dob});

  factory ForgotPassAuthRequestModel.fromJson(Map<String, dynamic> json) =>
      _$ForgotPassAuthRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$ForgotPassAuthRequestModelToJson(this);
}

@JsonSerializable()
class ForgotPassAuthResponseModel {
  @JsonKey(name: 'access_token')
  final String? accessToken;
  @Json<PERSON><PERSON>(name: 'token_type')
  final String? tokenType;

  const ForgotPassAuthResponseModel({
    this.accessToken,
    this.tokenType,
  });

  factory ForgotPassAuthResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ForgotPassAuthResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$ForgotPassAuthResponseModelToJson(this);
}
