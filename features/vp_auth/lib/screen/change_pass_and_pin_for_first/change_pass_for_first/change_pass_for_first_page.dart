import 'package:flutter/material.dart';
import 'package:vp_auth/cubit/change_pass_for_first/change_pass_for_first_cubit.dart';
import 'package:vp_auth/cubit/change_pass_for_first/change_pass_for_first_validate_cubit.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/router/account_router.dart';
import 'package:vp_auth/screen/change_pass_and_pin_for_first/widgets/pass_and_pin_input_field.dart';
import 'package:vp_common/vp_common.dart' hide Assets;
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class ChangePassForFirstPage extends StatefulWidget {
  final bool updatePin;

  const ChangePassForFirstPage({Key? key, required this.updatePin})
      : super(key: key);

  @override
  State<ChangePassForFirstPage> createState() => _ChangePassForFirstPageState();
}

class _ChangePassForFirstPageState extends State<ChangePassForFirstPage> {
  late final _currentPassController = InputValidatorController();
  late final _newPassController = InputValidatorController();
  late final _rePassController = InputValidatorController();
  late final _changePassForFirstValidateCubit =
      context.read<ChangePassForFirstValidateCubit>();

  @override
  void initState() {
    super.initState();
    _currentPassController
        .validate(_changePassForFirstValidateCubit.currentPasswordValidator);
    _newPassController
        .validate(_changePassForFirstValidateCubit.newPasswordValidator);
    _rePassController
        .validate(_changePassForFirstValidateCubit.rePasswordValidator);
  }

  @override
  void dispose() {
    _currentPassController.dispose();
    _newPassController.dispose();
    _rePassController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) =>
      BlocConsumer<ChangePassForFirstCubit, ChangePassForFirstState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            if (widget.updatePin) {
              context.replace(AccountRouter.changePinForFirst.routeName,
                  extra: true);
              return;
            } else {
              VPPopup.oneButton(
                title: S.current.account_change_pass_success,
                content: S.current.account_change_pass_success_description,
              )
                  .icSuccess
                  .copyWith(
                      button: VpsButton.primarySmall(
                    title: S.current.account_login,
                    onPressed: () => context.go(AccountRouter.signIn.routeName),
                  ))
                  .showDialog(context);
            }
          }
        },
        builder: (BuildContext context, ChangePassForFirstState state) {
          return Stack(
            children: [
              BlocBuilder<ChangePassForFirstValidateCubit,
                  ChangePassForFirstValidateState>(
                builder: (context, state) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 12.0),
                    Text(
                      S.current.account_please_enter_your_password,
                      style: context.textStyle.subtitle16
                          .copyColor(vpColor.textPrimary),
                    ),
                    const SizedBox(height: 16.0),
                    ChangeForFirstInputField(
                      hintText: S.current.account_current_password,
                      controller: _currentPassController,
                    ),
                    const SizedBox(height: 8.0),
                    ChangeForFirstInputField(
                      hintText: S.current.account_new_password,
                      controller: _newPassController,
                      inputType: state.newPass.inputType,
                    ),
                    const SizedBox(height: 8.0),
                    ChangeForFirstInputField(
                      hintText: S.current.account_enter_the_new_password,
                      controller: _rePassController,
                      inputType: state.rePass.inputType,
                      errorLabel: state.rePass.errorLabel,
                    ),
                    const SizedBox(height: 16.0),
                    InputValidatorLabel(data: state.newPassValidate),
                  ],
                ),
              ),
              if (state.status.isLoading) Center(child: VPBankLoading()),
            ],
          );
        },
      );
}
