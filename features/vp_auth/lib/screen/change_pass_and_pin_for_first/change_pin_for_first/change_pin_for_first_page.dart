import 'package:flutter/material.dart';
import 'package:vp_auth/cubit/change_pin_for_first/change_pin_for_first_cubit.dart';
import 'package:vp_auth/cubit/change_pin_for_first/change_pin_for_first_validate_cubit.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/router/account_router.dart';
import 'package:vp_auth/screen/change_pass_and_pin_for_first/widgets/pass_and_pin_input_field.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class ChangePinForFirst extends StatefulWidget {
  final bool? isStep2;

  const ChangePinForFirst({Key? key, this.isStep2}) : super(key: key);

  @override
  State<ChangePinForFirst> createState() => _ChangePinForFirstState();
}

class _ChangePinForFirstState extends State<ChangePinForFirst> {
  late final _currentPinController = InputValidatorController();
  late final _newPinController = InputValidatorController();
  late final _rePinController = InputValidatorController();
  late final _changePinForFirstValidateCubit =
      context.read<ChangePinForFirstValidateCubit>();

  @override
  void initState() {
    super.initState();
    _currentPinController
        .validate(_changePinForFirstValidateCubit.currentPinValidator);
    _newPinController.validate(_changePinForFirstValidateCubit.newPinValidator);
    _rePinController.validate(_changePinForFirstValidateCubit.rePinValidator);
  }

  @override
  void dispose() {
    _currentPinController.dispose();
    _newPinController.dispose();
    _rePinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) =>
      BlocConsumer<ChangePinForFirstCubit, ChangePinForFirstState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            VPPopup.oneButton(
              title: S.current.account_success,
              content: S.current.account_success_change_pass_pin,
            )
                .icSuccess
                .copyWith(
                    button: VpsButton.primarySmall(
                  title: S.current.account_login,
                  onPressed: () {
                    context.go(AccountRouter.signIn.routeName);
                  },
                ))
                .showDialog(context);
          }
        },
        builder: (BuildContext context, ChangePinForFirstState state) {
          return Stack(
            children: [
              BlocBuilder<ChangePinForFirstValidateCubit,
                  ChangePinForFirstValidateState>(
                builder: (context, state) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16.0),
                    Text(
                      S.current.account_change_pin_for_continue,
                      style: context.textStyle.subtitle16
                          .copyColor(vpColor.textPrimary),
                    ),
                    const SizedBox(height: 16.0),
                    ChangeForFirstInputField(
                      hintText: S.current.account_pin_is_sent,
                      controller: _currentPinController,
                      isPassword: false,
                    ),
                    const SizedBox(height: 8.0),
                    ChangeForFirstInputField(
                      hintText: S.current.account_new_pin,
                      controller: _newPinController,
                      inputType: state.newPin.inputType,
                      isPassword: false,
                    ),
                    const SizedBox(height: 8.0),
                    ChangeForFirstInputField(
                      hintText: S.current.account_enter_the_new_pin,
                      controller: _rePinController,
                      inputType: state.rePin.inputType,
                      errorLabel: state.rePin.errorLabel,
                      isPassword: false,
                    ),
                    const SizedBox(height: 16.0),
                    InputValidatorLabel(data: state.newPinValidate),
                  ],
                ),
              ),
              if (state.status.isLoading) Center(child: VPBankLoading()),
            ],
          );
        },
      );
}
