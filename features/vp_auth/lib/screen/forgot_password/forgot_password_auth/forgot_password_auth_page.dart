import 'package:flutter/material.dart';
import 'package:vp_auth/cubit/forgot_password/forgot_password_auth/forgot_password_gen_otp_cubit.dart';
import 'package:vp_auth/cubit/forgot_password/forgot_password_auth/forgot_password_verify_otp_cubit.dart';
import 'package:vp_auth/cubit/forgot_password/page/forgot_password_page_cubit.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/screen/widgets/otp_timer.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

import '../../widgets/app_pincode_widget.dart';

class ForgotPasswordAuthPage extends StatefulWidget {
  const ForgotPasswordAuthPage({super.key});

  @override
  State<ForgotPasswordAuthPage> createState() => _ForgotPasswordAuthPageState();
}

class _ForgotPasswordAuthPageState extends State<ForgotPasswordAuthPage> {
  late final ForgotPasswordGenOtpCubit _genOtpCubit;
  late final ForgotPasswordVerifyOtpCubit _verifyOtpCubit;
  late final otpTextController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _genOtpCubit = context.read<ForgotPasswordGenOtpCubit>();
    _verifyOtpCubit = context.read<ForgotPasswordVerifyOtpCubit>();
  }

  @override
  Widget build(context) => BlocProvider(
        create: (context) => _genOtpCubit,
        child: MultiBlocListener(
          listeners: [
            BlocListener<ForgotPasswordVerifyOtpCubit,
                ForgotPasswordVerifyOtpState>(
              listener: (context, state) => switch (state.status) {
                ForgotPasswordVerifyOtpStatus.success =>
                  context.read<ForgotPasswordPageCubit>().navigateNext(),
                ForgotPasswordVerifyOtpStatus.limitWrongOTP =>
                  _genOtpCubit.apiGenOTPResetPassword(),
                _ => null,
              },
            ),
            BlocListener<ForgotPasswordGenOtpCubit, ForgotPasswordGenOtpState>(
              listener: (context, state) {
                if (state.status.isLoading) {
                  otpTextController.clear();
                  _verifyOtpCubit.reset();
                }
              },
            ),
          ],
          child: Container(
            margin: EdgeInsets.only(top: 12.0),
            padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.0)),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    S.current.account_guide_input_email_or_phone,
                    style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                  ),
                  const SizedBox(height: 16.0),
                  AppPincodeWidget(
                    controller: otpTextController,
                    count: AppConfigUtils.appOTP,
                    onChanged: (value, count) {},
                    onCompleted: (otp) =>
                        _verifyOtpCubit.apiVerifyOTPResetPassword(
                      otp,
                      _genOtpCubit.phoneOrEmail,
                    ),
                  ),
                  BlocBuilder<ForgotPasswordVerifyOtpCubit,
                          ForgotPasswordVerifyOtpState>(
                      builder: (context, state) => state.status.message == null
                          ? SizedBox.shrink()
                          : Text(state.status.message!,
                              style: vpTextStyle.subtitle14
                                  ?.copyWith(color: context.colors.chartRed),
                              textAlign: TextAlign.center)),
                  const SizedBox(height: 8.0),
                  BlocBuilder<ForgotPasswordGenOtpCubit,
                          ForgotPasswordGenOtpState>(
                      builder: (context, state) => OtpTimer(
                            isSuccess: state.status.isSuccess,
                            message: state.status.message,
                            expiredOTP: _genOtpCubit.expiredOTP,
                          )),
                  TextButton(
                      onPressed: _genOtpCubit.apiGenOTPResetPassword,
                      child: Text(S.current.account_re_send_otp,
                          style: vpTextStyle.subtitle16.copyColor(themeData.primary))),
                ],
              ),
            ),
          ),
        ),
      );
}
