import 'package:flutter/material.dart';
import 'package:vp_auth/cubit/forgot_password/forgot_password_auth/forgot_password_gen_otp_cubit.dart';
import 'package:vp_auth/cubit/forgot_password/forgot_password_auth/forgot_password_verify_otp_cubit.dart';
import 'package:vp_auth/cubit/forgot_password/forgot_password_change/forgot_password_change_cubit.dart';
import 'package:vp_auth/cubit/forgot_password/forgot_password_change/forgot_password_change_validate_cubit.dart';
import 'package:vp_auth/cubit/forgot_password/forgot_password_verify/forgot_password_verify_validate_cubit.dart';
import 'package:vp_auth/cubit/forgot_password/page/forgot_password_page_cubit.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/router/account_router.dart';
import 'package:vp_common/extensions/popup_extensions.dart';
import 'package:vp_core/utils/go_router_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class ForgotPasswordPage extends StatefulWidget {
  final Widget child;

  const ForgotPasswordPage({super.key, required this.child});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  late final forgotPasswordPageCubit = ForgotPasswordPageCubit();

  @override
  Widget build(BuildContext context) => MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => forgotPasswordPageCubit),
          BlocProvider(
              create: (context) => ForgotPasswordVerifyValidateCubit()),
          BlocProvider(create: (context) => ForgotPasswordGenOtpCubit()),
          BlocProvider(create: (context) => ForgotPasswordVerifyOtpCubit()),
          BlocProvider(create: (context) => ForgotPasswordChangeCubit()),
          BlocProvider(
              create: (context) => ForgotPasswordChangeValidateCubit()),
        ],
        child: BlocListener<ForgotPasswordPageCubit, ForgotPasswordPageState>(
          listenWhen: (previous, current) => previous.index != current.index,
          listener: (context, state) {
            switch (state.index.index) {
              case 0:
                context.push(AccountRouter.forgotPasswordVerifyPage.routeName);
                break;
              case 1:
                context.replace(AccountRouter.forgotPasswordAuthPage.routeName);
                break;
              case 2:
                context
                    .replace(AccountRouter.forgotPasswordChangePage.routeName);
                break;
            }
          },
          child: VPScaffold(
            body: BlocBuilder<ForgotPasswordPageCubit, ForgotPasswordPageState>(
              builder: (context, state) => Column(
                children: [
                  VPAppBar.flows(
                    title: state.index.title,
                    leading: IconButton(
                      onPressed: () {
                        VPPopup.outlineAndPrimaryButton(
                                title: S.current.account_cancelChangePass,
                                content:
                                    S.current.account_cancelChangePassConfirm)
                            .icFail
                            .copyWith(
                                button: VpsButton.secondarySmall(
                              title: S.current.account_close,
                              onPressed: context.pop,
                            ))
                            .copyWith(
                                button: VpsButton.primaryDangerSmall(
                              title: S.current.account_cancel,
                              onPressed: () {
                                context.popUntilRoute(
                                    AccountRouter.signIn.routeName);
                              },
                            ))
                            .showDialog(context);
                      },
                      icon: DesignAssets.icons.appbar.icClose.svg(
                          colorFilter: ColorFilter.mode(
                              vpColor.iconPrimary, BlendMode.srcIn)),
                    ),
                  ),
                  Expanded(
                      child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.0),
                    child: widget.child,
                  )),
                  StepperAnimation(
                    config: StepConfig(
                        totalStep: 3, stepActive: state.index.index + 1),
                  ),
                  VPBottomActionView.onlyButton(
                    textButton: state.index.textButton,
                    onPressed: () {
                      switch (state.index) {
                        case ForgotPasswordIndex.verify:
                          final verifyState = context
                              .read<ForgotPasswordVerifyValidateCubit>()
                              .state;
                          context
                              .read<ForgotPasswordGenOtpCubit>()
                              .apiGenOTPResetPassword(
                                  idNumber: verifyState.idNumber.text,
                                  dob: verifyState.dateOfBirth.text,
                                  phoneOrEmail: verifyState.phone.text);
                          break;

                        case ForgotPasswordIndex.change:
                          final newPass = context
                              .read<ForgotPasswordChangeValidateCubit>()
                              .state
                              .newPass
                              .text;
                          final forgotPassAuthResponseModel = context
                              .read<ForgotPasswordVerifyOtpCubit>()
                              .state
                              .authResponseModel;
                          context
                              .read<ForgotPasswordChangeCubit>()
                              .modificationPassword(
                                  newPass, forgotPassAuthResponseModel);
                          break;

                        case _:
                          forgotPasswordPageCubit.navigateNext();
                      }
                    },
                    disabled: !state.buttonEnable,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
}
