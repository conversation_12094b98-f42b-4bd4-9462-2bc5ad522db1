import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:collection/collection.dart';
import 'package:ekyc_plugin_flutter/ekyc_plugin_flutter.dart';
import 'package:flutter/material.dart';
import 'package:vp_auth/core/constant/onboarding_path_api.dart';
import 'package:vp_auth/core/repository/onboarding_repository.dart';
import 'package:vp_auth/gen/assets.gen.dart' as assets;
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/model/sign_up/model_ekyc_fis/live_ness_model.dart';
import 'package:vp_auth/model/sign_up/register/onboarding/models/onboarding_users_ekyc_action_obj.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_utils.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/4.1sign_up_nfc/widget/popup_warning.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:vp_auth/utils/base_response.dart';
import 'package:vp_auth/utils/widgets/app_camera/app_camera_page.dart';
import 'package:vp_auth/utils/widgets/dialog_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/app_device_id.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

part 'sign_up_ekyc_state.dart';

class SignUpEkycCubit extends ISignUpController<SignUpEkycState> {
  SignUpEkycCubit() : super(const SignUpEkycState());

  @override
  String? get title => S.current.account_e_kyc_title;

  @override
  int? get progress => SignUpStep.ekyc.process;

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.ekyc;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    checkEmitEkycLiveness();
  }

  bool isCanEkyc() {
    return state.isValid ?? false;
  }

  void checkEmitEkyc(dynamic value) {
    String msgEkyc = '';
    final _isValid = (value is String) ? value.isEmpty : null;
    if (_isValid == false) {
      msgEkyc = S.current.account_verify_face_fail;
    }
    setEnable(_isValid ?? false);
    (_isValid ?? false)
        ? SignUpTracking().signupFaceRegSuccess()
        : SignUpTracking().signupFaceRegFail();

    emit(state.copyWith(isValid: _isValid, message: msgEkyc));
  }

  void checkEmitEkycLiveness() {
    SignUpData().attachDataObj.setEnableFaceLiveness(true);
    setEnable(true);
    emit(state.copyWith(isValid: true, message: ''));
    SignUpRouter().onPush(SignUpTypeScreen.signature);
  }

  void updateEnableButtonLiveness() {
    emit(state.copyWith(
        isValid: SignUpData().attachDataObj.getEnableFaceLiveness()));
  }

  Future<OnboardingUsersAccEkycActionResponseObj?>
      callAPIGetListAction() async {
    OnboardingUsersAccEkycActionResponseObj? obj;
    try {
      showDialogLoading();
      final value = await GetIt.instance
          .get<OnboardingRepository>()
          .onboardingUsersEkycAction();

      if (value != null) {
        if (value.isSuccess()) {
          obj = value.data;
        } else {
          showMessage(value.message);
        }
      }
    } catch (e) {
      showError(e);
    } finally {
      hideDialogLoading();
    }
    return obj;
  }

  OnboardingRepository get repository =>
      GetIt.instance.get<OnboardingRepository>();

  Future<void> gotoFaceLiveness(BuildContext context) async {
    final obj = SignUpData().attachDataObj;
    Map ressss = await SampleCallNativeFlutter.showLiveness(
      '${SharedPref.getString(KeyShared.onboardingToken)}',
      'Transcode',
      F.url + OnboardingPathAPI.onboardingLivenessV2,
      'vi',
      obj.getBase64FrontIdCard(),
      headers: {
        "x-device": await AppUtils.getFpjsDeviceId(),
        "x-devicetype": Platform.isAndroid ? "Android" : "IOS",
        "x-lang": "vi",
        "x-via": "Y",
      },
    ) as Map;

    ResponseLivenessResponse response =
        ResponseLivenessResponse.fromJson(ressss);
    if (response.error?.type == 'UNKNOWN_ERROR') {
      popupWarning(
          GetIt.instance<NavigationService>().navigatorKey.currentContext!,
          onBack: () => SignUpRouter().onBackUtil(SignUpTypeScreen.phone));

      return;
    }
    ResponseLiveness liveNessModel =
        ResponseLiveness.fromJson(json.decode(ressss['response']));
    if (liveNessModel.code == 'IABNOT000') {
      await Future.delayed(const Duration(milliseconds: 1000));
      checkEmitEkycLiveness();
    } else {
      showSnackBar(context, liveNessModel.message ?? '');
    }
  }

  Future<void> gotoCameraSignature(BuildContext context) async {
    final isSuccess = await SignUpPermissionUtils.checkPermissionCamera();
    if (isSuccess) {
      final cameras = await availableCameras();
      final camera = cameras.firstWhereOrNull(
          (element) => element.lensDirection == CameraLensDirection.front);

      if (cameras.isNotEmpty && camera != null) {
        RouterUtils.push(
            GetIt.instance<NavigationService>().navigatorKey.currentContext!,
            AppCameraPage(
              camera: camera,
              title: 'Chụp ảnh chân dung',
              nameFileSave: 'IDCARD_SIGNATURE',
              onTap: (pathSave, context) async {
                if (pathSave != null && pathSave is String) {
                  try {
                    showDialogLoading();
                    final obj = await GetIt.instance
                        .get<OnboardingRepository>()
                        .updateAvatar(pathFile: pathSave) as BEBaseResponse;
                    hideDialogLoading();
                    if (obj.isSuccess()) {
                      Navigator.of(context).pop();
                      Navigator.of(context).pop();
                      showDialogLoading();
                      await Future.delayed(const Duration(seconds: 2));
                      hideDialogLoading();
                      gotoFaceLiveness(context);
                    } else {
                      final message = await getErrorMessage(obj);
                      showDialogError(
                          img: assets.Assets.icons.icError,
                          GetIt.instance<NavigationService>()
                              .navigatorKey
                              .currentContext!,
                          S.current.account_notification,
                          message ?? '', pressRight: () {
                        Navigator.of(context).pop();
                      });
                    }
                  } catch (e) {
                    hideDialogLoading();
                    final message = await getErrorMessage(e);
                    showDialogError(
                        img: assets.Assets.icons.icError,
                        GetIt.instance<NavigationService>()
                            .navigatorKey
                            .currentContext!,
                        S.current.account_notification,
                        message ?? '', pressRight: () {
                      Navigator.of(context).pop();
                    });
                  }
                }
              },
            ),
            dataBack: (pathSave) {});
      }
    }
  }
}
