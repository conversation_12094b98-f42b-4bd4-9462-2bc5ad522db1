import 'package:flutter/material.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class OtpTimer extends StatelessWidget {
  final bool isSuccess;
  final String message;
  final VoidCallback expiredOTP;

  const OtpTimer({
    super.key,
    required this.isSuccess,
    required this.message,
    required this.expiredOTP,
  });

  @override
  Widget build(BuildContext context) => isSuccess
      ? TweenAnimationBuilder<double>(
          tween: Tween(begin: 180.0, end: 0.0),
          duration: const Duration(seconds: 180),
          builder: (_, value, child) {
            final seconds = value.toInt();
            if (seconds == 0) {
              expiredOTP();
            }
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(S.current.account_counting_otp,
                    style:
                        vpTextStyle.subtitle14.copyColor(vpColor.textPrimary)),
                Text(' ${seconds < 10 ? '0' : ''}$seconds',
                    style:
                        vpTextStyle.subtitle14.copyColor(themeData.primary)),
                Text(' s',
                    style:
                        vpTextStyle.subtitle14.copyColor(vpColor.textPrimary)),
              ],
            );
          },
        )
      : Text(message,
          style: vpTextStyle.subtitle14
              ?.copyWith(color: context.colors.chartRed),
          textAlign: TextAlign.center);
}
