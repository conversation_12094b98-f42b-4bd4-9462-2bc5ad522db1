import 'dart:async';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:vp_auth/utils/widgets/app_camera/bloc/app_camera/app_camera_bloc.dart';
import 'package:vp_common/widget/vpbank_loading.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_design_system/vp_design_system.dart';
import '../../../../gen/assets.gen.dart';
import 'account_ekyc_paint.dart';

class AppCameraPage extends StatefulWidget {
  const AppCameraPage({
    Key? key,
    required this.camera,
    this.title,
    this.nameFileSave,
    this.onTap,
  }) : super(key: key);

  final CameraDescription camera;
  final String? title;
  final String? nameFileSave;
  final Function? onTap;

  @override
  State<AppCameraPage> createState() => AppCameraPageState();
}

class AppCameraPageState extends State<AppCameraPage> {
  late CameraController controller;
  late Future<void> initiallizeControllerFuture;
  final bloc = AppCameraBloc();
  bool havePathImage = false;
  String pathSave = '';

  @override
  void initState() {
    super.initState();

    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    controller = CameraController(
      widget.camera,
      ResolutionPreset.high,
      enableAudio: false,
    );

    initiallizeControllerFuture =
        controller.initialize()..then((_) {
          controller.lockCaptureOrientation(DeviceOrientation.portraitUp);
        });

    bloc.controllerData.stream.listen((pathSave) {
      if (pathSave.isNotEmpty) {
        setState(() {
          havePathImage = true;
          this.pathSave = pathSave;
          showAcceptImageBottomSheet(context);
        });
      } else {
        showMessage(
          'Chụp ảnh không thành công. Vui lòng kiểm tra lại quyền truy cập Camera của thiết bị',
        );
      }
    });
  }

  @override
  void dispose() {
    controller.dispose();
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.black87,
        appBar: buildAppBar(context),
        body: buildCameraView(),
      ),
    );
  }

  /*-------- Build AppBar -------- */
  AppBar buildAppBar(BuildContext context) {
    return AppBar(
      title: Text(
        widget.title ?? '',
        style: vpTextStyle.headineBold6?.copyWith(color: ColorDefine.white),
      ),
      backgroundColor: Colors.black,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Padding(
          padding: EdgeInsets.only(left: 8, top: 4),
          child: Icon(Icons.close, color: ColorDefine.white),
        ),
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
      ),
    );
  }

  /*-------- Build CameraView -------- */
  FutureBuilder<void> buildCameraView() {
    return FutureBuilder<void>(
      future: initiallizeControllerFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          final cameraPreview = CameraPreview(controller);

          return Stack(
            children: [
              CustomPaint(
                foregroundPainter: AccountEkycPaint(),
                child: SizedBox(
                  width: controller.value.previewSize?.width,
                  height: controller.value.previewSize?.height,
                  child: cameraPreview,
                ),
              ),
              // ),
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: ClipPath(
                  clipper: AccountEkycClip(),
                  child:
                      havePathImage
                          ? Image.file(File(pathSave))
                          : cameraPreview,
                ),
              ),
              buildBottomCamera(),
              buildLoading(),
            ],
          );
        } else {
          return const Center(child: CircularProgressIndicator());
        }
      },
    );
  }

  /*-------- Build Bottom View -------- */
  Align buildBottomCamera() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: double.infinity,
        height: 200,
        color: Colors.black,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 24, right: 48, left: 48),
              child: Text(
                'Vui lòng đặt khuôn mặt của bạn trong khung tròn và chụp ảnh',
                style: vpTextStyle.body14.copyColor(ColorDefine.white),
                textAlign: TextAlign.center,
              ),
            ),
            Expanded(
              child: Align(
                alignment: Alignment.center,
                child: InkWell(
                  onTap: () => captureImage(),
                  child: SvgPicture.asset(Assets.icons.icBtnCamera),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /*-------- Build ViewLoadingAndDialog -------- */
  StreamBuilder buildLoading() {
    return StreamBuilder<bool>(
      stream: bloc.controllerLoading.stream,
      builder: (context, snapshot) {
        final isShowLoading = snapshot.data ?? false;
        if (isShowLoading) {
          return Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            color: Colors.black.withValues(alpha: 0.7),
            child: const Center(child: VPBankLoading()),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  /*-------- Xử lý chụp ảnh -------- */
  Future<void> captureImage() async {
    // bloc.sinkState(AccountCameraOcrBlocState.loading);
    bloc.sinkLoading(true);
    await initiallizeControllerFuture;
    controller.setFlashMode(FlashMode.off);
    final imageXfile = await controller.takePicture();
    bloc.saveImageIDCard(
      nameFileSave: widget.nameFileSave ?? 'IMAGE_CAPTURE',
      pathXFile: imageXfile.path,
    );
  }

  Future<void> showAcceptImageBottomSheet(BuildContext context) async {
    VPPopup.bottomSheet(
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Bạn có muốn dùng ảnh này?',
                style: vpTextStyle.headineBold6?.copyWith(
                  color: themeData.black,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Hãy đảm bảo ảnh đã chụp rõ ràng, khuôn mặt nằm trong khung hình.',
                style: vpTextStyle.body14.copyColor(themeData.black),
              ),
            ],
          ),
        )
        .copyWith(
          button: VpsButton.secondarySmall(
            title: 'Chụp lại',
            onPressed: () {
              Navigator.of(context).pop();
              setState(() => havePathImage = false);
            },
          ),
        )
        .copyWith(
          button: VpsButton.primarySmall(
            title: 'Có, dùng ảnh này',
            onPressed: () => widget.onTap!(pathSave, context),
          ),
        )
        .showSheet(context);
  }
}
