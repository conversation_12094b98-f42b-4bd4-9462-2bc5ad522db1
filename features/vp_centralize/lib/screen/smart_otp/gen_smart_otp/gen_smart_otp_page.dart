import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_centralize/localization/localize_key.dart';
import 'package:vp_centralize/screen/smart_otp/custom_scaffold.dart';
import 'package:vp_centralize/screen/smart_otp/gen_smart_otp/gen_smart_otp_state.dart';
import 'package:vp_centralize/screen/smart_otp/rx_view.dart';
import 'package:vp_centralize/screen/widgets/button/button_widget.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

import 'gen_smart_otp_cubit.dart';

class GenSmartOTPPage extends StatefulWidget {
  const GenSmartOTPPage({
    required this.pinCode,
    required this.accountNo,
    super.key,
  });

  final String pinCode;

  final String? accountNo;

  @override
  State<GenSmartOTPPage> createState() => _GetSmartOtpState();
}

class _GetSmartOtpState extends State<GenSmartOTPPage> {
  late GenSmartOTPCubit cubit = GenSmartOTPCubit(
    pinCode: widget.pinCode,
    accountNo: widget.accountNo,
  );

  @override
  void dispose() {
    cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<GenSmartOTPCubit>.value(
      value: cubit,
      child: CustomScaffold(
        child: SafeArea(
          child: Column(
            children: [
              buildHeaderView(),

              /// content view
              Expanded(child: buildContentView()),

              buildBottomActionView(),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildBottomActionView() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: themeData.divider)),
      ),
      child: ButtonWidget(
        action: 'Hoàn thành',
        onPressed: () => Navigator.pop(context, cubit.otp.value),
      ),
    );
  }

  Widget buildContentView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      child: Column(
        children: [
          Text(
            LocalizeKey.genSmartOTPTitle,
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ),

          const SizedBox(height: 24),

          /// build smart otp code
          buildSmartOTPCodeView(),

          const SizedBox(height: 24),

          /// build timer view
          buildTimerView(),
        ],
      ),
    );
  }

  Widget buildTimerView() {
    return BlocBuilder<GenSmartOTPCubit, GenSmartOTPAbstractState>(
      builder: (_, state) {
        return CountdownTimerView(
          otp: state.otp,
          remainTime: state.remainTime,
          totalTime: state.totalTime,
          onEnd: () => cubit.genSmartOTP(),
        );
      },
    );
  }

  Widget buildSmartOTPCodeView() {
    return Container(
      width: double.infinity,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: themeData.primary16,
      ),
      padding: const EdgeInsets.symmetric(vertical: 24),
      child: RxView<String>(
        rxSubject: cubit.otp,
        builder: (_, smartOTOP) {
          return Text(
            smartOTOP.insertCharacterAt(3, ' ') ?? '',
            style: vpTextStyle.headline4.copyColor(themeData.primary),
          );
        },
      ),
    );
  }

  Widget buildHeaderView() {
    return HeaderWidget(
      back: false,
      actionRight: () => Navigator.pop(context),
      subTitle: LocalizeKey.genSmartOTPAppBarTitle,
      title: LocalizeKey.genSmartOTPAppBarSubTitle,
      icon: Icon(CupertinoIcons.clear, color: themeData.gray700),
    );
  }
}

class CountdownTimerView extends StatefulWidget {
  const CountdownTimerView({
    required this.otp,
    required this.remainTime,
    required this.totalTime,
    this.onEnd,
    super.key,
  });

  final String otp;

  final int remainTime;

  final int totalTime;

  final VoidCallback? onEnd;

  @override
  State<CountdownTimerView> createState() => _CountdownTimerViewState();
}

class _CountdownTimerViewState extends State<CountdownTimerView>
    with TickerProviderStateMixin {
  AnimationController? _controller;

  final style = vpTextStyle.captionMedium;

  double progress = 0;

  @override
  void didUpdateWidget(covariant CountdownTimerView oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.otp != widget.otp) {
      createController();
    }
  }

  void createController() {
    if (widget.remainTime <= 0) return;

    _controller?.dispose();

    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: widget.remainTime),
    );

    _controller?.forward();

    _controller?.addListener(listener);

    _controller?.addStatusListener(statusListener);
  }

  void statusListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      widget.onEnd?.call();
    }
  }

  void listener() {
    if (!mounted) return;

    final value = widget.remainTime - (animValue * widget.remainTime);

    setState(() => progress = value / widget.totalTime);
  }

  double get animValue => _controller?.value ?? 0;

  int get time => (widget.remainTime - (widget.remainTime * animValue)).ceil();

  @override
  void initState() {
    super.initState();

    createController();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            minHeight: 2,
            value: progress,
            backgroundColor: themeData.gray100,
            valueColor: AlwaysStoppedAnimation(themeData.primary),
          ),
        ),
        const SizedBox(height: 16),
        RichText(
          text: TextSpan(
            style: style.copyColor(themeData.gray700),
            children: [
              const TextSpan(text: LocalizeKey.genSmartOTPTimer),
              TextSpan(
                text: ' ${time}s',
                style: style.copyColor(themeData.primary),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _controller?.dispose();

    super.dispose();
  }
}
