import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/core/service/ipo_ambassador_service.dart';
import 'package:vp_ipo/model/ipo_ambassador_model.dart';

abstract class IpoAmbassadorRepository {
  Future<BaseResponse<IpoAmbassadorModel>> getInfoAmbassador();
  Future<BaseResponse<IpoAmbassadorModel>> updateInfoAmbassador(
    IpoAmbassadorRequestModel request,
  );

  Future<BaseResponse<IpoAmbassadorModel>> createInfoAmbassador(
    IpoAmbassadorRequestModel request,
  );
}

class IpoAmbassadorRepositoryImpl extends IpoAmbassadorRepository {
  IpoAmbassadorRepositoryImpl({required this.ipoAmbassadorService});
  final IpoAmbassadorService ipoAmbassadorService;

  @override
  Future<BaseResponse<IpoAmbassadorModel>> getInfoAmbassador() async {
    try {
      return ipoAmbassadorService.getInfoAmbassador();
    } catch (e, stackTrace) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<IpoAmbassadorModel>> updateInfoAmbassador(
    IpoAmbassadorRequestModel request,
  ) async {
    try {
      return ipoAmbassadorService.updateInfoAmbassador(request);
    } catch (e, stackTrace) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<IpoAmbassadorModel>> createInfoAmbassador(
    IpoAmbassadorRequestModel request,
  ) async {
    try {
      return ipoAmbassadorService.createInfoAmbassador(request);
    } catch (e, stackTrace) {
      rethrow;
    }
  }
}
