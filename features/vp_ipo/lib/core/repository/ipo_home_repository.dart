import 'package:vp_common/vp_common.dart';
import 'package:vp_core/base/base_response/base_response.dart';
import 'package:vp_core/cubit/auth_cubit.dart';
import 'package:vp_ipo/core/service/ipo_home_service.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_info_model.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_order_model.dart';

abstract class IpoHomeRepository {
  Future<BaseResponse<IpoOrderResponse>?> ipoOrder(
      {String status = "A", String? startDate, String? endDate});
  Future<BaseResponse<List<IpoInfoModel>>> ipoInfo();
}

class IpoHomeRepositoryImpl extends IpoHomeRepository {
  IpoHomeRepositoryImpl({required this.ipoHomeService});
  final IpoHomeService ipoHomeService;

  @override
  Future<BaseResponse<List<IpoInfoModel>>> ipoInfo() async {
    try {
      var result = await ipoHomeService.ipoInfo(requestId: AppHelper().genXRequestID());
      return result;
    } catch (e, stackTrace) {
      print(e);
      rethrow;
    }
  }

  @override
  Future<BaseResponse<IpoOrderResponse>?> ipoOrder(
      {String status= "A", String? startDate, String? endDate}) async {
    try {
      String? custodycd = GetIt.instance<AuthCubit>().userInfo?.userinfo?.custodycd;

      var result = await ipoHomeService.ipoOrder(
        custodycd: custodycd ?? "116C0001",
        requestId: AppHelper().genXRequestID(),
        status: status,
        fromDate: startDate,
        toDate: endDate
      );
      return result;
    } catch (e, stackTrace) {
      return null;
    }
  }
}
