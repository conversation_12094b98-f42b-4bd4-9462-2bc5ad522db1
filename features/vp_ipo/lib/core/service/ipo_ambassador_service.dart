import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/core/constant/ipo_path_api.dart';
import 'package:vp_ipo/model/ipo_ambassador_model.dart';

part 'ipo_ambassador_service.g.dart';

@RestApi()
abstract class IpoAmbassadorService {
  factory IpoAmbassadorService(Dio dio, {String baseUrl}) =
      _IpoAmbassadorService;

  @GET(IpoPathApi.ipoAmbassador)
  Future<BaseResponse<IpoAmbassadorModel>> getInfoAmbassador();

  @PUT(IpoPathApi.ipoAmbassador)
  Future<BaseResponse<IpoAmbassadorModel>> updateInfoAmbassador(
    @Body() IpoAmbassadorRequestModel request,
  );

  @POST(IpoPathApi.ipoAmbassador)
  Future<BaseResponse<IpoAmbassadorModel>> createInfoAmbassador(
    @Body() IpoAmbassadorRequestModel request,
  );
}
