import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/core/constant/ipo_path_api.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_info_model.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_order_model.dart';

part 'ipo_home_service.g.dart';

@RestApi()
abstract class IpoHomeService {
  factory IpoHomeService(Dio dio, {String baseUrl}) = _IpoHomeService;

  @GET(IpoPathApi.ipoInfo)
  Future<BaseResponse<List<IpoInfoModel>>> ipoInfo({
    @Header('x-request-id') String requestId = "",
    @Query("stId") String stId = "VPX",
    @Query("partner") String partner = "VPBS",
    @Query("channel") String channel = "OL"
  });

  @GET(IpoPathApi.ipoOrder)
  Future<BaseResponse<IpoOrderResponse>> ipoOrder({
    @Path("custodycd") String custodycd = "116C0001",
    @Header('x-request-id') String requestId = "",
    @Query("fromdate") String? fromDate,
    @Query("todate") String? toDate,
    @Query("status") String status = "A",
  });
}
