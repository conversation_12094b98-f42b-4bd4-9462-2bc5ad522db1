import 'dart:ui';

import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/core/repository/ipo_ambassador_repository.dart';
import 'package:vp_ipo/model/ipo_ambassador_model.dart';
import 'package:vp_ipo/screen/ipo_ambassador/widget/chooose_company_widget.dart';
import 'package:vp_ipo/utils/loading_utils.dart';

part 'info_ambassador_state.dart';

class InfoAmbassadorCubit extends Cubit<InfoAmbassadorState> {
  InfoAmbassadorCubit() : super(const InfoAmbassadorState());
  IpoAmbassadorRepository get _ipoAmbassadorRepository =>
      GetIt.instance.get<IpoAmbassadorRepository>();

  Future<void> getInfoAmbassador() async {
    emit(state.copyWith(status: InfoAmbassadorStatus.loading));
    try {
      final response = await _ipoAmbassadorRepository.getInfoAmbassador();
      if (response.isSuccess && response.code != "IABERR1123") {
        emit(
          state.copyWith(
            ipoAmbassadorModel: response.data,
            status: InfoAmbassadorStatus.success,
          ),
        );
      } else if (response.code == "IABERR1123") {
        emit(state.copyWith(status: InfoAmbassadorStatus.noCreate));
      } else {
        showError("Có lỗi xảy ra vui lòng thử lại sau");
        emit(
          state.copyWith(
            status: InfoAmbassadorStatus.failure,
            error: "Có lỗi xảy ra vui lòng thử lại sau",
          ),
        );
      }
    } catch (e, _) {
      showError(e);
      emit(
        state.copyWith(
          status: InfoAmbassadorStatus.failure,
          error: e.toString(),
        ),
      );
    }
  }

  void updateInfo(CompanyItem item, VoidCallback onSuccess) async {
    LoadingUtil.showLoading();
    try {
      final request = IpoAmbassadorRequestModel(
        email: item.email,
        organization: item.name,
      );
      final repsonse = await _ipoAmbassadorRepository.updateInfoAmbassador(
        request,
      );
      LoadingUtil.hideLoading();
      emit(
        state.copyWith(
          ipoAmbassadorModel: repsonse.data,
          status: InfoAmbassadorStatus.success,
        ),
      );

      onSuccess.call();
    } catch (e) {
      LoadingUtil.hideLoading();
      showError(e);
    }
  }

  void createInfoAmbassador(CompanyItem item, VoidCallback onSuccess) async {
    LoadingUtil.showLoading();
    try {
      final request = IpoAmbassadorRequestModel(
        email: item.email,
        organization: item.name,
      );
      await _ipoAmbassadorRepository.createInfoAmbassador(request);
      LoadingUtil.hideLoading();
      onSuccess.call();
    } catch (e) {
      LoadingUtil.hideLoading();

      showError(e);
    }
  }
}
