part of 'info_ambassador_cubit.dart';

enum InfoAmbassadorStatus { initial, loading, success, failure, noCreate }

extension InfoAmbassadorStatusX on InfoAmbassadorStatus {
  bool get isInitial => this == InfoAmbassadorStatus.initial;
  bool get isLoading => this == InfoAmbassadorStatus.loading;
  bool get isSuccess => this == InfoAmbassadorStatus.success;
  bool get isFailure => this == InfoAmbassadorStatus.failure;
  bool get isNoCreate => this == InfoAmbassadorStatus.noCreate;
}

class InfoAmbassadorState extends Equatable {
  const InfoAmbassadorState({
    this.ipoAmbassadorModel,
    this.status = InfoAmbassadorStatus.initial,
    this.error,
  });
  final InfoAmbassadorStatus status;
  final IpoAmbassadorModel? ipoAmbassadorModel;
  final String? error;

  @override
  List<Object?> get props => [status, ipoAmbassadorModel, error];

  InfoAmbassadorState copyWith({
    InfoAmbassadorStatus? status,
    IpoAmbassadorModel? ipoAmbassadorModel,
    String? error,
  }) {
    return InfoAmbassadorState(
      ipoAmbassadorModel: ipoAmbassadorModel ?? this.ipoAmbassadorModel,
      status: status ?? this.status,
      error: error ?? this.error,
    );
  }
}
