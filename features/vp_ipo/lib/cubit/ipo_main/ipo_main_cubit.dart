import 'package:intl/intl.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/core/repository/ipo_home_repository.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_order_state.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_home_order_info_model.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_info_model.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_order_model.dart';
import 'package:vp_ipo/utils/utils.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'ipo_main_state.dart';

enum TypePageIpo { main, order, history }

enum IPOStatusType {
  s0001,
  s0002,
  s0003_1,
  s0003_2,
  s0004,
  s0005,
  s0006,
  s0007,
  s0008,
  unknow
}

enum PeriodIPOHigh {
  register,
  alloAndPayment,
  complete,
  unknow
}

enum IPOStatusContract {
  waitingDeposit,
  deposited,
  waitingPayment,
  paymented,
  complete,
  failed,
  unknow
}

extension IPOStatusTypeX on IPOStatusType {
  String get text {
    return toString().split('.').last.toUpperCase();
  }
}

enum StatusAlloType {
  complete,
  pending,
  cancel,
  processing,
  unknow
}

enum PeriodIPO { //date + time
  periodPrepare,
  periodRegister,
  periodAllocate,
  periodPayment,
  periodComplete,
  periodUnknow
}

enum PeriodIPOSlide { //only date, for ui slide
  periodRegister,
  periodAllocate,
  periodPayment,
  periodComplete,
  periodUnknow
}


class IpoMainCubit extends Cubit<IpoMainState> {
  IpoMainCubit()
    : super(
      IpoMainState(
        typePageCurrent: TypePageIpo.main,
        typePage: TypePageIpo.main,
        ipoInfoModel: IpoInfoModel(),
        listNeedPay: [],
        ipoHomeOrderInfoModel: IpoHomeOrderInfoModel()
      ),
    );

  final IpoHomeRepository _ipoHomeRepository = GetIt.instance<IpoHomeRepository>();

  Future<void> loadData() async {
    ServerTimeKeeper? serverTimeKeeper = await getTimeServer();
    update(serverTimeKeeper: serverTimeKeeper);

    BaseResponse<List<IpoInfoModel>> resultInfo = await _ipoHomeRepository.ipoInfo();
    if (resultInfo.data != null && resultInfo.data!.isNotEmpty) {
      IpoInfoModel ipoInfoModel = resultInfo.data!.first;
      //TODO: fake
      // ipoInfoModel.paymentStartDate = DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.now().subtract(const Duration(days: 2)));
      // ipoInfoModel.paymentEndDate = DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.now().add(const Duration(days: 10)));
      // ipoInfoModel.reEndDate = DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.now().add(const Duration(days: 10)));
      // ipoInfoModel.startDateAllo = DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.now().add(const Duration(seconds: 8)));
      // ipoInfoModel.endDateAllo = DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.now().add(const Duration(seconds: 1000)));
      var mapPeriod = mapDataForPeriodIPO(ipoInfoModel, state.serverTimeKeeper?.current ?? DateTime.now());
      update(periodIPO: mapPeriod.periodIPO, periodIPOSlide: mapPeriod.periodIPOSlide);
      update(ipoInfoModel: ipoInfoModel);
    }

    BaseResponse<IpoOrderResponse>? resultOrder = await _ipoHomeRepository.ipoOrder();
    if (resultOrder?.data != null) {
      updatePeriodIPO(resultOrder!.data!.data, state.periodIPO);
      update(ipoOrderResponse: resultOrder.data!);
      _isShowWarningPayment();
    }

    makeDataForViewInfoOrder();
  }

  static Future<ServerTimeKeeper?> getTimeServer() async {
    // DateTime serverDate = await GetIt.instance.get<StockCommonRepository>().getCurrentTime();
    // return ServerTimeKeeper(serverDate);
    //TODO: fake
    return ServerTimeKeeper(DateTime.now());
  }

  refreshUI() {
    if (state.ipoInfoModel != null) {
      var mapPeriod = mapDataForPeriodIPO(state.ipoInfoModel!, state.serverTimeKeeper?.current ?? DateTime.now());
      update(periodIPO: mapPeriod.periodIPO, periodIPOSlide: mapPeriod.periodIPOSlide);
      _isShowWarningPayment();
      makeDataForViewInfoOrder();
    }
  }

  void changePage(TypePageIpo typePageIpo) {
    if (state.typePage == TypePageIpo.order && typePageIpo == TypePageIpo.order) {
      update(
        typePageCurrent: state.typePageCurrent,
        typePage: state.typePageCurrent);
    }
    update(
      typePageCurrent: typePageIpo != TypePageIpo.order ? typePageIpo : state.typePageCurrent,
      typePage: typePageIpo,
    );
  }

  static ({PeriodIPO periodIPO, PeriodIPOSlide periodIPOSlide}) mapDataForPeriodIPO(IpoInfoModel ipoInfoModel, DateTime dateTimeNow) {
    PeriodIPO periodIPO = PeriodIPO.periodUnknow;
    PeriodIPOSlide periodIPOSlide = PeriodIPOSlide.periodUnknow;

    //enum for slide
    if (compareDateSameDayOrDiff(dateTimeNow, ipoInfoModel.endDateTime, isAfter: true, isDayOnly: true)) {
      periodIPOSlide = PeriodIPOSlide.periodComplete;
    } else if (compareDateSameDayOrDiff(dateTimeNow, ipoInfoModel.startDateAlloTime, isAfter: true, isDayOnly: true)) {
      periodIPOSlide = PeriodIPOSlide.periodAllocate;
    }  else if (compareDateSameDayOrDiff(dateTimeNow, ipoInfoModel.paymentStartDateTime, isAfter: true, isDayOnly: true)) {
      periodIPOSlide = PeriodIPOSlide.periodPayment;
    }else if (compareDateSameDayOrDiff(dateTimeNow, ipoInfoModel.reStartDateTime, isAfter: true, isDayOnly: true)) {
      periodIPOSlide = PeriodIPOSlide.periodRegister;
    }

    //enum for card
    if (ipoInfoModel.reStartDateTime != null && dateTimeNow.isBefore(ipoInfoModel.reStartDateTime!)) {
      periodIPO = PeriodIPO.periodPrepare;
    } else if (
      compareDateSameDayOrDiff(ipoInfoModel.reStartDateTime, dateTimeNow, isBefore: true) &&
      compareDateSameDayOrDiff(dateTimeNow, ipoInfoModel.reEndDateTime, isBefore: true)) {
      periodIPO = PeriodIPO.periodRegister;
    } else if (
      compareDateSameDayOrDiff(ipoInfoModel.startDateAlloTime, dateTimeNow, isBefore: true) &&
      compareDateSameDayOrDiff(dateTimeNow, ipoInfoModel.endDateAlloTime, isBefore: true)) {
      periodIPO = PeriodIPO.periodAllocate;
    } else if (
      compareDateSameDayOrDiff(ipoInfoModel.paymentStartDateTime, dateTimeNow, isBefore: true) &&
      compareDateSameDayOrDiff(dateTimeNow, ipoInfoModel.paymentEndDateTime, isBefore: true)) {
      periodIPO = PeriodIPO.periodPayment;
    } else if (compareDateSameDayOrDiff(dateTimeNow, ipoInfoModel.endDateTime, isBefore: true)) {
      periodIPO = PeriodIPO.periodComplete;
    }
    return (periodIPO: periodIPO, periodIPOSlide: periodIPOSlide);
  }

  bool _isShowWarningPayment() {
    if (state.periodIPO == PeriodIPO.periodPrepare ||
        state.periodIPO == PeriodIPO.periodAllocate ||
        state.periodIPO == PeriodIPO.periodComplete ||
        state.periodIPO == PeriodIPO.periodUnknow) {
       return false;
    } else {
      List<IpoOrderModel>? listFilter = state.ipoOrderResponse?.data.whereStatus(multiOrderStatus:
      [state.periodIPO == PeriodIPO.periodRegister ? IPOStatusType.s0001 : IPOStatusType.s0003_1]);
      emit(state.copyWith(listNeedPay: listFilter ?? []));
      return (listFilter != null && listFilter.isNotEmpty) ? true : false;
    }
  }

  IpoHomeOrderInfoModel? makeDataForViewInfoOrder() {
    if (state.periodIPO == PeriodIPO.periodUnknow || state.periodIPO == PeriodIPO.periodPrepare) {
      return null;
    }
    IpoHomeOrderInfoModel ipoHomeOrderInfoModel = IpoHomeOrderInfoModel();
    List<IpoOrderModel>? listFilter = [];
    if (state.periodIPO == PeriodIPO.periodPrepare ||
        state.periodIPO == PeriodIPO.periodUnknow) {
      return null;
    }

    if (state.periodIPO == PeriodIPO.periodRegister) {
      listFilter = state.ipoOrderResponse?.data.whereStatus(multiOrderStatus: [IPOStatusType.s0002]);
      if (listFilter == null || listFilter.isEmpty) return null;
      ipoHomeOrderInfoModel.titleSum = "Khối lượng đăng ký";
      ipoHomeOrderInfoModel.titleAllo = "Giá trị đăng ký";
      ipoHomeOrderInfoModel.titlePayment = "Giá trị cọc";
      ipoHomeOrderInfoModel.titleButton = "Mua thêm";
      ipoHomeOrderInfoModel.valueSum = "${(listFilter.sumQuantity()).toFormat3()} CP";
      ipoHomeOrderInfoModel.valueAllo = "${state.ipoInfoModel?.price?.toFormat3()} đ/CP";
      ipoHomeOrderInfoModel.valuePayment = "${(listFilter.sumDeposit()).toFormat3()} đ";
    } else if (state.periodIPO == PeriodIPO.periodAllocate) {
      listFilter = state.ipoOrderResponse?.data.whereStatus(multiOrderStatus: [IPOStatusType.s0003_1, IPOStatusType.s0003_2]);
      ipoHomeOrderInfoModel.titleSum = "Khối lượng phân bổ";
      ipoHomeOrderInfoModel.titleAllo = "Giá trị phân bổ";
      ipoHomeOrderInfoModel.titlePayment = "Giá trị cọc";
      ipoHomeOrderInfoModel.titleButton = "Tra cứu";
      ipoHomeOrderInfoModel.valueSum = (listFilter == null || listFilter.isEmpty) ? "-" : "${listFilter.sumQuantityAllo().toFormat3()} CP";
      ipoHomeOrderInfoModel.valueAllo = (listFilter == null || listFilter.isEmpty) ? "-" :
        "${(listFilter.sumTotalPayment()).toFormat3()} đ";
      ipoHomeOrderInfoModel.valuePayment = "${state.ipoOrderResponse?.totalPaymentAmount.toFormat3()} đ";
    } else if (state.periodIPO == PeriodIPO.periodPayment) {
      listFilter = state.ipoOrderResponse?.data.whereStatus(multiOrderStatus: [IPOStatusType.s0003_1, IPOStatusType.s0003_2, IPOStatusType.s0004]);
      if (listFilter == null || listFilter.isEmpty) return null;
      ipoHomeOrderInfoModel.titleSum = "Khối lượng phân bổ";
      ipoHomeOrderInfoModel.titleAllo = "Giá trị phân bổ";
      ipoHomeOrderInfoModel.titlePayment = "Giá trị cần thanh toán";
      ipoHomeOrderInfoModel.titleButton = "Thanh toán";
      ipoHomeOrderInfoModel.valueSum = "${(listFilter.sumQuantityAllo()).toFormat3()} CP";
      ipoHomeOrderInfoModel.valueAllo = "${(listFilter.sumValueAllo()).toFormat3()} đ";
      ipoHomeOrderInfoModel.valuePayment = "${state.ipoOrderResponse?.totalRemPaymentAllo.toFormat3()} đ";
    } else if (state.periodIPO == PeriodIPO.periodComplete) {
      listFilter = state.ipoOrderResponse?.data.whereStatus(multiOrderStatus: [IPOStatusType.s0005]);
      List<IpoOrderModel>? listFilterTemp = (listFilter == null || listFilter.isEmpty) ?
        state.ipoOrderResponse?.data.whereStatus(multiOrderStatus: [IPOStatusType.s0006]) : listFilter;
      List<IpoOrderModel>? listFilterSuccess = listFilterTemp?.where((e){
        return e.statusAllo == "Complete";
      }).toList();
      ipoHomeOrderInfoModel.titleSum = "Khối lượng phân bổ";
      ipoHomeOrderInfoModel.titleAllo = "Giá trị phân bổ";
      ipoHomeOrderInfoModel.titlePayment = null;
      ipoHomeOrderInfoModel.titleButton = "Tra cứu";
      ipoHomeOrderInfoModel.valueSum = (listFilter == null || listFilter.isEmpty) ? "-" : "${(listFilter.sumQuantityAllo()).toFormat3()} CP";
      ipoHomeOrderInfoModel.valueAllo = (listFilterSuccess == null || listFilterSuccess.isEmpty) ? "-" : "${(listFilterSuccess.sumValueAllo()).toFormat3()} đ";
    }
    emit(state.copyWith(ipoHomeOrderInfoModel: ipoHomeOrderInfoModel));
    return null;
  }

  isTimeToOrder() {
    return false;
  }

  void update({
    IpoInfoModel? ipoInfoModel,
    PeriodIPO? periodIPO,
    PeriodIPOSlide? periodIPOSlide,
    TypePageIpo? typePage,
    IpoOrderResponse? ipoOrderResponse,
    TypePageIpo? typePageCurrent,
    List<IpoOrderModel>? listNeedPay,
    IpoHomeOrderInfoModel? ipoHomeOrderInfoModel,
    ServerTimeKeeper? serverTimeKeeper
  }) {
    emit(state.copyWith(
      ipoInfoModel: ipoInfoModel,
      ipoOrderResponse: ipoOrderResponse,
      periodIPO: periodIPO,
      periodIPOSlide: periodIPOSlide,
      typePage: typePage,
      typePageCurrent: typePageCurrent,
      listNeedPay: listNeedPay,
      ipoHomeOrderInfoModel: ipoHomeOrderInfoModel,
      serverTimeKeeper: serverTimeKeeper
    ));
  }
}
