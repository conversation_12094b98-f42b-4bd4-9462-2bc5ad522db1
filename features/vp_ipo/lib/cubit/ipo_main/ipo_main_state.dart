part of 'ipo_main_cubit.dart';

class IpoMainState {
  const IpoMainState({
    this.typePageCurrent = TypePageIpo.main,
    this.typePage = TypePageIpo.main,
    this.ipoInfoModel,
    this.ipoOrderResponse,
    this.periodIPO = PeriodIPO.periodUnknow,
    this.periodIPOSlide = PeriodIPOSlide.periodUnknow,
    this.listNeedPay = const [],
    this.ipoHomeOrderInfoModel,
    this.serverTimeKeeper
  });
  final TypePageIpo typePage;
  final TypePageIpo typePageCurrent;
  final IpoInfoModel? ipoInfoModel;
  final IpoOrderResponse? ipoOrderResponse;
  final PeriodIPO periodIPO;
  final PeriodIPOSlide periodIPOSlide;
  final List<IpoOrderModel> listNeedPay;
  final IpoHomeOrderInfoModel? ipoHomeOrderInfoModel;
  final ServerTimeKeeper? serverTimeKeeper;

  IpoMainState copyWith({TypePageIpo? typePageCurrent, TypePageIpo? typePage,
    IpoInfoModel? ipoInfoModel, IpoOrderResponse? ipoOrderResponse,
    PeriodIPO? periodIPO, PeriodIPOSlide? periodIPOSlide,
    List<IpoOrderModel>? listNeedPay, IpoHomeOrderInfoModel? ipoHomeOrderInfoModel,
    ServerTimeKeeper? serverTimeKeeper}) {

    return IpoMainState(
      typePageCurrent: typePageCurrent ?? this.typePageCurrent,
      typePage: typePage ?? this.typePage,
      ipoInfoModel: ipoInfoModel ?? this.ipoInfoModel,
      ipoOrderResponse: ipoOrderResponse ?? this.ipoOrderResponse,
      periodIPO: periodIPO ?? this.periodIPO,
      periodIPOSlide: periodIPOSlide ?? this.periodIPOSlide,
      listNeedPay: listNeedPay ?? this.listNeedPay,
      ipoHomeOrderInfoModel: ipoHomeOrderInfoModel ?? this.ipoHomeOrderInfoModel,
      serverTimeKeeper: serverTimeKeeper ?? this.serverTimeKeeper
    );
  }
}
