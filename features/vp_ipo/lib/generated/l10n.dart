// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class VPIpoLocalize {
  VPIpoLocalize();

  static VPIpoLocalize? _current;

  static VPIpoLocalize get current {
    assert(
      _current != null,
      'No instance of VPIpoLocalize was loaded. Try to initialize the VPIpoLocalize delegate before accessing VPIpoLocalize.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<VPIpoLocalize> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = VPIpoLocalize();
      VPIpoLocalize._current = instance;

      return instance;
    });
  }

  static VPIpoLocalize of(BuildContext context) {
    final instance = VPIpoLocalize.maybeOf(context);
    assert(
      instance != null,
      'No instance of VPIpoLocalize present in the widget tree. Did you add VPIpoLocalize.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static VPIpoLocalize? maybeOf(BuildContext context) {
    return Localizations.of<VPIpoLocalize>(context, VPIpoLocalize);
  }

  /// `info`
  String get ipo_home {
    return Intl.message('info', name: 'ipo_home', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<VPIpoLocalize> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<VPIpoLocalize> load(Locale locale) => VPIpoLocalize.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
