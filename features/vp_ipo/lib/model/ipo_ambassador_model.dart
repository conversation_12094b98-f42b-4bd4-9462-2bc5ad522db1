import 'package:json_annotation/json_annotation.dart';

part 'ipo_ambassador_model.g.dart';

@JsonSerializable()
class IpoAmbassadorModel {
  IpoAmbassadorModel({this.accountNo, this.organization, this.email});
  String? accountNo;
  String? organization;
  String? email;

  factory IpoAmbassadorModel.fromJson(Map<String, dynamic> json) =>
      _$IpoAmbassadorModelFromJson(json);

  Map<String, dynamic> toJson() => _$IpoAmbassadorModelToJson(this);
}

@JsonSerializable()
class IpoAmbassadorRequestModel {
  IpoAmbassadorRequestModel({
    required this.email,
    required this.organization,
    this.extraData = "",
  });
  String email;
  String organization;
  String extraData;

  factory IpoAmbassadorRequestModel.fromJson(Map<String, dynamic> json) =>
      _$IpoAmbassadorRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$IpoAmbassadorRequestModelToJson(this);
}
