// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ipo_ambassador_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

IpoAmbassadorModel _$IpoAmbassadorModelFromJson(Map<String, dynamic> json) =>
    IpoAmbassadorModel(
      accountNo: json['accountNo'] as String?,
      organization: json['organization'] as String?,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$IpoAmbassadorModelToJson(IpoAmbassadorModel instance) =>
    <String, dynamic>{
      'accountNo': instance.accountNo,
      'organization': instance.organization,
      'email': instance.email,
    };

IpoAmbassadorRequestModel _$IpoAmbassadorRequestModelFromJson(
  Map<String, dynamic> json,
) => IpoAmbassadorRequestModel(
  email: json['email'] as String,
  organization: json['organization'] as String,
  extraData: json['extraData'] as String? ?? "",
);

Map<String, dynamic> _$IpoAmbassadorRequestModelToJson(
  IpoAmbassadorRequestModel instance,
) => <String, dynamic>{
  'email': instance.email,
  'organization': instance.organization,
  'extraData': instance.extraData,
};
