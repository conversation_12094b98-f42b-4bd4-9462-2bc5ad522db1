import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_share/flutter_share.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/info_ambassador/info_ambassador_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_ambassador/widget/ambassador_qr_dialog.dart';
import 'package:vp_ipo/utils/share_utils.dart';

import 'widget/chooose_company_widget.dart';

class IpoAmbassadorScreen extends StatefulWidget {
  const IpoAmbassadorScreen({super.key});

  @override
  State<IpoAmbassadorScreen> createState() => _IpoAmbassadorScreenState();
}

class _IpoAmbassadorScreenState extends State<IpoAmbassadorScreen> {
  CompanyItem companyItem = listCompany.first;
  bool isValid = false;
  bool isEdit = false;
  GlobalKey globalKey = GlobalKey();

  final _txtEmail = TextEditingController();
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => InfoAmbassadorCubit()..getInfoAmbassador(),
      child: VPScaffold(
        appBar: VPAppBar.layer(title: "Đại sứ IPO VPBankS"),
        backgroundColor: vpColor.backgroundElevationMinus1,
        body: SafeArea(child: _body()),
        bottomNavigationBar: _bottom(),
      ),
    );
  }

  Widget _body() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: BlocBuilder<InfoAmbassadorCubit, InfoAmbassadorState>(
        builder: (context, state) {
          if (state.status.isSuccess) {
            return _haveAmbassador(
              state.ipoAmbassadorModel?.organization,
              state.ipoAmbassadorModel?.email,
            );
          }
          if (state.status.isLoading) {
            return Column(
              children: [
                _banner(),
                Expanded(child: const Center(child: VPBankLoading())),
              ],
            );
          }
          if (state.status.isNoCreate) {
            return _noAmbassador();
          }
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              Column(children: [_banner()]),
            ],
          );
        },
      ),
    );
  }

  Widget _noAmbassador() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _banner(),
          const SizedBox(height: 8),
          _description(),
          const SizedBox(height: 8),
          _step(),
          const SizedBox(height: 12),
          _infoCompany(),
          const SizedBox(height: 12),
          Align(
            child: Text(
              textAlign: TextAlign.center,
              "Nhập đúng email để đảm bảo được nhận thưởng nhé!",
              style: vpTextStyle.captionRegular.copyColor(
                vpColor.textAccentRed,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _haveAmbassador(String? name, String? email) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _banner(),
          const SizedBox(height: 8),
          Column(
            children: [
              Text(
                "Có tin vui phải báo, có deal tốt phải chia! Nhanh tay giới thiệu cơ hội mua cổ phiếu VPBankS IPO tới bạn bè",
                style: vpTextStyle.body14.copyColor(vpColor.textSecondary),
              ),
              const SizedBox(height: 16),
              Text(
                "Nhận ngay 0.4% giá trị giao dịch khi bạn bè mua VPX thành công ",
                style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
              ),
              const SizedBox(height: 8),
              isEdit
                  ? _infoCompany()
                  : Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: vpColor.backgroundElevation0,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: AmbassadorQrInfo(
                      keyQR: globalKey,
                      item: CompanyItem(name: name ?? "", email: email ?? ""),
                    ),
                  ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _step() {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: vpColor.backgroundElevation1,
              borderRadius: BorderRadius.circular(8),
            ),
            height: 112,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                VpIpoAssets.icons.ipoStep1.svg(),
                const SizedBox(height: 4),
                Text(
                  "Tạo mã QR\nvà Chia sẻ",
                  style: vpTextStyle.captionRegular.copyColor(
                    vpColor.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: vpColor.backgroundElevation1,
              borderRadius: BorderRadius.circular(8),
            ),
            height: 112,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                VpIpoAssets.icons.ipoStep2.svg(),
                const SizedBox(height: 4),
                Text(
                  "Bạn bè quét\nQR và Đặt\nmua VPX",
                  style: vpTextStyle.captionRegular.copyColor(
                    vpColor.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: vpColor.backgroundElevation1,
              borderRadius: BorderRadius.circular(8),
            ),
            height: 112,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                VpIpoAssets.icons.ipoStep3.svg(),
                const SizedBox(height: 4),
                Text(
                  "Bạn bè mua\nVPX thành công",
                  style: vpTextStyle.captionRegular.copyColor(
                    vpColor.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _banner() {
    return VpImage.fromAsset(asset: VpIpoAssets.images.bannerAm.image());
  }

  void validateError() {
    setState(() {
      isValid = _txtEmail.text.isNotEmpty && !_txtEmail.text.contains(" ");
    });
  }

  Widget _infoCompany() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation1,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Công ty thành viên",
            style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
          ),
          const SizedBox(height: 4),

          SizedBox(
            width: double.infinity,
            child: VPDropdownView.large(
              value: companyItem.name,
              onTap: () async {
                showSelectCompany(context, (item) {
                  setState(() {
                    companyItem = item;
                  });
                });
              },
            ),
          ),
          const SizedBox(height: 12),
          Text(
            "Email công ty",
            style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
          ),
          const SizedBox(height: 4),
          VPTextField(
            inputFormatters: [FilteringTextInputFormatter.deny(RegExp(' '))],
            autofocus: false,
            keyboardType: TextInputType.emailAddress,
            maxLength: 255,
            hintText: 'Nhập thông tin...',
            controller: _txtEmail,
            onChanged: (value) {
              validateError();
            },
            suffixIcon:
                (_) => Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                  ).copyWith(top: 12),
                  child: Text(
                    "@${companyItem.email}",
                    style: vpTextStyle.body14.copyColor(vpColor.textTertiary),
                  ),
                ),
          ),
        ],
      ),
    );
  }

  Widget _description() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: vpColor.backgroundAccentRed2,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            "Chỉ dành cho CBNV hệ sinh thái",
            style: vpTextStyle.captionSemiBold.copyColor(vpColor.textWhite),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          "Có tin vui phải báo, có deal tốt phải chia! Nhanh tay giới thiệu cơ hội mua cổ phiếu IPO VPBankS (VPX) tới bạn bè",
          style: vpTextStyle.body14.copyColor(vpColor.textSecondary),
        ),
        const SizedBox(height: 12),
        Text(
          "Nhận ngay 0.4% giá trị giao dịch của bạn bè chỉ với 3 bước cơ bản ",
          style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
        ),
      ],
    );
  }

  Widget _bottom() {
    return BlocBuilder<InfoAmbassadorCubit, InfoAmbassadorState>(
      builder: (context, state) {
        if (!state.status.isSuccess && !state.status.isNoCreate) {
          return const SizedBox();
        }
        return Container(
          decoration: BoxDecoration(
            color: vpColor.backgroundElevation1,
            border: Border(
              top: BorderSide(color: vpColor.strokeNormal, width: 1),
            ),
          ),
          padding: const EdgeInsets.all(
            16.0,
          ).copyWith(bottom: 16 + MediaQuery.of(context).padding.bottom),
          child:
              state.status.isNoCreate || isEdit
                  ? VpsButton.primarySmall(
                    disabled: !isValid,
                    onPressed: () {
                      if (isEdit) {
                        context.read<InfoAmbassadorCubit>().updateInfo(
                          companyItem.copyWith(
                            email: "${_txtEmail.text}@${companyItem.email}",
                          ),
                          () {
                            setState(() {
                              isEdit = false;
                            });
                            showInfoAmbassadorQrDialog(
                              context,
                              companyItem.copyWith(
                                email: "${_txtEmail.text}@${companyItem.email}",
                              ),
                            );
                          },
                        );
                      } else {
                        context.read<InfoAmbassadorCubit>().createInfoAmbassador(
                          companyItem.copyWith(
                            email: "${_txtEmail.text}@${companyItem.email}",
                          ),
                          () {
                            showInfoAmbassadorQrDialog(
                              context,
                              companyItem.copyWith(
                                email: "${_txtEmail.text}@${companyItem.email}",
                              ),
                            );
                          },
                        );
                      }
                    },
                    title: isEdit ? "Xác nhận" : "Tạo mã QR ngay",
                    width: double.infinity,
                    icon: VpIpoAssets.icons.icIpoShare.svg(
                      colorFilter: ColorFilter.mode(
                        vpColor.textWhite,
                        BlendMode.srcIn,
                      ),
                    ),
                  )
                  : Row(
                    children: [
                      Expanded(
                        child: VpsButton.secondarySmall(
                          onPressed: () {
                            final item = listCompany.findFirstOrNull(
                              (element) =>
                                  element.name ==
                                  state.ipoAmbassadorModel?.organization,
                            );
                            setState(() {
                              isEdit = true;
                              if (item != null) {
                                companyItem = item;
                                _txtEmail.text =
                                    (state.ipoAmbassadorModel?.email ?? "")
                                        .split("@")
                                        .first;
                                validateError();
                              }
                            });
                          },
                          title: "Chỉnh sửa QR",
                          width: double.infinity,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: VpsButton.primarySmall(
                          onPressed: () {
                            ShareUtils.shareQrcode(globalKey);
                          },
                          title: "Chia sẻ ngay",
                          width: double.infinity,
                          icon: VpIpoAssets.icons.icIpoShare.svg(
                            colorFilter: ColorFilter.mode(
                              vpColor.textWhite,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
        );
      },
    );
  }
}
