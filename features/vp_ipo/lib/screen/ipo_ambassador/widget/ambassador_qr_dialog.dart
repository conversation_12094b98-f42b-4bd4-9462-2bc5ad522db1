import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:vp_common/utils/copy_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_ambassador/widget/chooose_company_widget.dart';
import 'package:vp_ipo/utils/share_utils.dart';

String get link {
  return "https://vpbanks.onelink.me/XNEk/OrderIPO?userId=${GetIt.instance<AuthCubit>().userInfo?.userinfo?.custid ?? ""}";
}

class AmbassadorQrDialog extends StatefulWidget {
  final CompanyItem item;
  const AmbassadorQrDialog({super.key, required this.item});

  @override
  State<AmbassadorQrDialog> createState() => _AmbassadorQrDialogState();
}

class _AmbassadorQrDialogState extends State<AmbassadorQrDialog> {
  GlobalKey globalKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 530,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Chia sẻ ngay tới bạn bè",
                style: vpTextStyle.headineBold6.copyColor(vpColor.textPrimary),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Assets.icons.icClose.svg(),
              ),
            ],
          ),

          Expanded(
            child: DefaultTabController(
              length: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  VPTabBar(tabs: [Tab(text: 'Mã QR'), Tab(text: 'Liên kết')]),
                  Expanded(
                    child: TabBarView(
                      children: [
                        AmbassadorQrInfo(
                          item: widget.item,
                          keyQR: globalKey,
                          isDialog: true,
                        ),
                        _infoLink(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _infoLink(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        _InfoName(email: widget.item.email, name: widget.item.name),
        const SizedBox(height: 12),
        Text(
          "Chia sẻ liên kết - Gia tăng thu nhập",
          style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
        ),
        const SizedBox(height: 8),
        Text(
          "Chia sẻ link để bắt đầu hành trình tăng thu nhập nhé. Rất nhiều phần quà đang chờ đón bạn khi bạn tham gia cùng VPBankS!",
          style: vpTextStyle.body14.copyColor(vpColor.textSecondary),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ).copyWith(bottom: 12),
                decoration: BoxDecoration(
                  color: vpColor.backgroundElevationMinus2,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: vpColor.strokeNormal),
                ),
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  link,
                  style: vpTextStyle.body14.copyColor(vpColor.textDisabled),
                ),
              ),
            ),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () {
                copyToClipboard(context, link);
              },
              child: SvgPicture.asset(
                VpIpoAssets.icons.icCopy.path,
                package: 'vp_ipo',
                colorFilter: ColorFilter.mode(
                  vpColor.iconPrimary,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class AmbassadorQrInfo extends StatelessWidget {
  final bool isDialog;
  final GlobalKey keyQR;
  final CompanyItem item;
  const AmbassadorQrInfo({
    super.key,
    required this.item,
    required this.keyQR,
    this.isDialog = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _InfoName(email: item.email, name: item.name),
        const SizedBox(height: 16),
        Align(
          child: Text(
            "Quét mã QR",
            style: vpTextStyle.body14.copyColor(vpColor.textSecondary),
          ),
        ),

        Align(
          child: RepaintBoundary(
            key: keyQR,
            child: QrImageView(
              data: link,
              size: 120,
              embeddedImageStyle: QrEmbeddedImageStyle(size: Size(16, 16)),
              errorCorrectionLevel: QrErrorCorrectLevel.Q,
              embeddedImage: VpIpoAssets.images.circleLogo.provider(),
              backgroundColor: themeData.white,
            ),
          ),
        ),
        Text(
          "Đặc quyền đầu tư cho Cổ đông VPBankS",
          style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
        ),
        const SizedBox(height: 8),
        Text(
          ". Giảm ngay 30% khi nâng cấp gói VIP StockGuru - tư vấn chuyên sâu không giới hạn.\n. Lãi suất Margin chỉ từ 6.6% - gia tăng sức mạnh vốn, bứt phá lợi nhuận!",
          style: vpTextStyle.body14.copyColor(vpColor.textSecondary),
        ),
        const SizedBox(height: 20),
        isDialog
            ? Row(
              children: [
                Expanded(
                  child: VpsButton.secondarySmall(
                    onPressed: () {
                      ShareUtils.saveImageQr(keyQR, context);
                    },
                    title: "Tải xuống",
                    width: double.infinity,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: VpsButton.primarySmall(
                    onPressed: () {
                      ShareUtils.shareQrcode(keyQR);
                    },
                    title: "Chia sẻ",
                    width: double.infinity,
                    icon: VpIpoAssets.icons.icIpoShare.svg(
                      colorFilter: ColorFilter.mode(
                        vpColor.textWhite,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ],
            )
            : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Đường dẫn link",
                  style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ).copyWith(bottom: 12),
                        decoration: BoxDecoration(
                          color: vpColor.backgroundElevationMinus2,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: vpColor.strokeNormal),
                        ),
                        child: Text(
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          link,
                          style: vpTextStyle.body14.copyColor(
                            vpColor.textDisabled,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () {
                        copyToClipboard(context, link);
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100),
                          border: Border.all(color: vpColor.strokeNormal),
                        ),
                        child: Center(
                          child: SvgPicture.asset(
                            VpIpoAssets.icons.icCopy.path,
                            package: 'vp_ipo',
                            colorFilter: ColorFilter.mode(
                              vpColor.iconPrimary,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
      ],
    );
  }
}

class _InfoName extends StatelessWidget {
  final String name;
  final String email;
  const _InfoName({required this.name, required this.email});

  Widget _avatar() {
    final userInfo = GetIt.instance<AuthCubit>().userInfo?.userinfo;
    final avtUrl = userInfo?.avtUrl;

    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: themeData.primary,
      ),
      width: 40,
      height: 40,
      alignment: Alignment.center,
      child:
          avtUrl != null
              ? CachedNetworkImage(
                placeholder: (context, url) => avtName(),
                imageBuilder:
                    (context, imageProvider) => Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        image: DecorationImage(
                          image: imageProvider,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                imageUrl: avtUrl,
                errorWidget: (_, __, ___) => avtName(),
              )
              : avtName(),
    );
  }

  Widget avtName() {
    return Text(
      GetIt.instance<AuthCubit>().userInfo?.userinfo?.fullname?.getInitials() ??
          '',
      style: vpTextStyle.subtitle16.copyColor(themeData.textEnable),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: vpColor.backgroundElevationMinus1,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          _avatar(),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${name} - ${GetIt.instance<AuthCubit>().userInfo?.userinfo?.fullname}",
                style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
              ),
              Text(
                GetIt.instance<AuthCubit>().userInfo?.userinfo?.custid ?? "",
                style: vpTextStyle.captionMedium.copyColor(vpColor.textPrimary),
              ),
              Text(
                email,
                style: vpTextStyle.captionMedium.copyColor(vpColor.textPrimary),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

showInfoAmbassadorQrDialog(BuildContext context, CompanyItem item) {
  VPPopup.custom(
    padding: const EdgeInsets.all(20),
    child: Container(
      width: MediaQuery.sizeOf(context).width,
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        borderRadius: BorderRadius.circular(12),
      ),
      child: AmbassadorQrDialog(item: item),
    ),
  ).showDialog(context);
}
