// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

Future<dynamic> showSelectCompany(
  BuildContext context,
  Function(CompanyItem) onTap,
) async {
  await showModalBottomSheet(
    barrierColor: themeData.overlayBottomSheet,
    context: context,
    backgroundColor: Colors.transparent,
    isDismissible: true,
    builder: (context) {
      return SafeArea(
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height / 2,
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: themeData.bgPopup,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          alignment: Alignment.center,
                          child: Text(
                            "Chọn công ty",
                            style: vpTextStyle.subtitle14.copyColor(
                              vpColor.textPrimary,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Flexible(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              color: themeData.bgPopup,
                            ),
                            child: Scrollbar(
                              thumbVisibility: true,
                              trackVisibility: true,
                              radius: const Radius.circular(5),
                              interactive: true,
                              child: ListView.builder(
                                itemCount: listCompany.length,
                                shrinkWrap: true,
                                itemBuilder: (context, index) {
                                  return InkWell(
                                    onTap: () {
                                      onTap.call(listCompany[index]);
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(
                                            width: 0.5,
                                            color: themeData.divider,
                                          ),
                                        ),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 16,
                                      ),
                                      child: Text(
                                        listCompany[index].name,

                                        style: vpTextStyle.subtitle16.copyColor(
                                          themeData.black,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                ButtonBottomSheet(
                  text: "Huỷ",
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
  return null;
}

class CompanyItem {
  final String name;
  final String email;
  CompanyItem({required this.name, required this.email});

  CompanyItem copyWith({
    String? name,
    String? email,
  }) {
    return CompanyItem(
      name: name ?? this.name,
      email: email ?? this.email,
    );
  }
}

final listCompany = [
  CompanyItem(name: "VPBankS", email: "vpbanks.com.vn"),
  CompanyItem(name: "VPBank", email: "vpbank.com.vn"),
  CompanyItem(name: "BE", email: "be.com.vn"),
  CompanyItem(name: "CAKE", email: "cake.com.vn"),
  CompanyItem(name: "LynkiD", email: "lynkid.com.vn"),
  CompanyItem(name: "OPES", email: "opes.com.vn"),
  CompanyItem(name: "FE Credit", email: "fecredit.com.vn"),
  CompanyItem(name: "GPBank", email: "gpbank.com.vn"),
];
