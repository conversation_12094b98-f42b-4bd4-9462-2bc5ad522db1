import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/core/repository/ipo_home_repository.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_order_state.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_order_model.dart';
import 'package:vp_ipo/screen/ipo_history/views/ipo_history_filter_bottomsheet.dart';
import 'package:vp_ipo/utils/utils.dart';

import 'ipo_history_state.dart';

class IpoHistoryCubit extends Cubit<IpoHistoryState> {
  IpoHistoryCubit() : super(IpoHistoryState(
    startDate: AppTimeUtils.format(
      minusMonths(DateTime.now(), 3),
      AppTimeUtilsFormat.dateNormal,
    ),
    endDate: AppTimeUtils.format(
      DateTime.now(),
      AppTimeUtilsFormat.dateNormal,
    ),
    filterStatus: filterDefault
  ));
  Future<void> initData(List<IpoOrderModel> listParent) async {
    emit(state.copyWith(listData: listParent.groupByTransactionDate()));
  }

  final IpoHomeRepository _ipoHomeRepository = GetIt.instance<IpoHomeRepository>();

  Future<void> loadData({FilterStatus? filterStatus, String? startDate, String? endDate, PeriodIPO? periodIPO}) async {
    String start = startDate ?? AppTimeUtils.format(
      minusMonths(DateTime.now(), 3),
      AppTimeUtilsFormat.dateNormal,
    );
    String end = endDate ?? AppTimeUtils.format(
      DateTime.now(),
      AppTimeUtilsFormat.dateNormal,
    );

    updateForFilter(start, end, filterStatus ?? filterDefault);

    BaseResponse<IpoOrderResponse>? resultOrder =
      await _ipoHomeRepository.ipoOrder(status: "A", startDate: start, endDate: end);
    if (resultOrder?.data != null) {
      List<IpoOrderModel> listData = resultOrder?.data?.data ?? [];
      updatePeriodIPO(listData, periodIPO ?? PeriodIPO.periodUnknow);
      if (filterStatus != null && filterStatus.index != 0) {
        listData = listData.whereStatus(multiOrderStatus: filterStatus.listStatus);
      }
      List<List<IpoOrderModel>> result = listData.groupByTransactionDate();
      emit(state.copyWith(listData: result, isApiError: false));
    } else {
      emit(state.copyWith(isApiError: true));
    }

    isShowButtonPayNav(periodIPO ?? PeriodIPO.periodUnknow, resultOrder?.data?.data ?? []);
  }

  void toggle(IpoOrderModel ipoOrderModel) {
    final updated = Set<IpoOrderModel>.from(state.selectedItemsPayment);
    if (updated.contains(ipoOrderModel)) {
      updated.remove(ipoOrderModel);
    } else {
      updated.add(ipoOrderModel);
    }
    emit(state.copyWith(selectedItemsPayment: updated));
  }

  void toggleAll() {
    final allItems = state.listData.expand((group) => group).toSet();
    emit(state.copyWith(
      selectedItemsPayment: allItems,
    ));
  }

  void clearSelected() => emit(state.copyWith(selectedItemsPayment: {}));

  updateForFilter(String startDate, String endDate, FilterStatus filterStatus) {
    emit(state.copyWith(startDate: startDate, endDate: endDate, filterStatus: filterStatus));
  }

  isShowButtonPayNav(PeriodIPO periodIPO, List<IpoOrderModel> listData) {
    if ((periodIPO == PeriodIPO.periodRegister || periodIPO == PeriodIPO.periodPayment) && listData.isNotEmpty) {
      var listTemp = listData.whereStatus(multiOrderStatus: [periodIPO == PeriodIPO.periodRegister ? IPOStatusType.s0001 : IPOStatusType.s0003_1]);
      emit(state.copyWith(listCanPayment: listTemp.isEmpty ? [] : listTemp));
      return;
    }
    emit(state.copyWith(listCanPayment: []));
  }
}

void updatePeriodIPO(List<IpoOrderModel> listData, PeriodIPO periodIPO) {
  for (var e in listData) {
    e.periodIPO = periodIPO;
  }
}