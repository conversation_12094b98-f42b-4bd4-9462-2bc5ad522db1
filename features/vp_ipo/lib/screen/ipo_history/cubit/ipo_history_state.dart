import 'package:vp_ipo/screen/ipo_history/views/ipo_history_filter_bottomsheet.dart';

import 'model/ipo_order_model.dart';

class IpoHistoryState {
  List<List<IpoOrderModel>> listData;
  List<IpoOrderModel> listCanPayment;
  final Set<IpoOrderModel> selectedItemsPayment;
  String? startDate;
  String? endDate;
  FilterStatus? filterStatus;
  bool isApiError;

  IpoHistoryState({
    this.listData = const [],
    this.selectedItemsPayment = const {},
    this.startDate,
    this.endDate,
    this.filterStatus,
    this.isApiError = false,
    this.listCanPayment = const []
  });

  IpoHistoryState copyWith({
    List<List<IpoOrderModel>>? listData,
    Set<IpoOrderModel>? selectedItemsPayment,
    String? startDate,
    String? endDate,
    FilterStatus? filterStatus,
    bool? isApiError,
    List<IpoOrderModel>? listCanPayment
  }) {
    return IpoHistoryState(
      listData: listData ?? this.listData,
      selectedItemsPayment: selectedItemsPayment ?? this.selectedItemsPayment,
      filterStatus: filterStatus ?? this.filterStatus,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isApiError: isApiError ?? this.isApiError,
      listCanPayment: listCanPayment ?? this.listCanPayment
    );
  }
}
