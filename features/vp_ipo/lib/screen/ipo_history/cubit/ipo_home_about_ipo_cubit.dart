import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_home_cubit.dart';

class IpoHomeAboutIPOCubit extends Cubit<void> {
  IpoHomeCubit ipoHomeCubit;
  IpoHomeAboutIPOCubit(this.ipoHomeCubit) : super(null);

  void changeExpand(bool isExpanded) {
    ipoHomeCubit.update(isExpanded: isExpanded);
  }

  void changeQAExpand(bool isQAExpanded) {
    ipoHomeCubit.update(isQAExpanded: isQAExpanded);
  }
}
