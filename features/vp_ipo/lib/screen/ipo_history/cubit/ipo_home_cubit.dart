import 'package:vp_core/vp_core.dart';

import 'ipo_home_state.dart';
import 'model/ipo_home_about_ipo_model.dart';


class IpoHomeCubit extends Cubit<IpoHomeState> {
  IpoHomeCubit() : super(IpoHomeState(
      ipoHomeAboutIPOModel: IpoHomeAboutIPOModel(isExpanded: true, isQAExpanded: true),
      isExpandAboutVPBS: true));

  Future<void> loadData() async {
  }

  void update({bool? isExpanded, bool? isQAExpanded, bool? isExpandAboutVPBS}) {
    emit(state.copyWith(ipoHomeAboutIPOModel:
      state.ipoHomeAboutIPOModel.copyWith(
        isExpanded: isExpanded,
        isQAExpanded: isQAExpanded
      ),
      isExpandAboutVPBS: isExpandAboutVPBS
    ));
  }
}