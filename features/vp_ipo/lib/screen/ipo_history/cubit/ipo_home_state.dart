import 'model/ipo_home_about_ipo_model.dart';

class IpoHomeState {
  final IpoHomeAboutIPOModel ipoHomeAboutIPOModel;
  final bool isExpandAboutVPBS;

  IpoHomeState({
    required this.ipoHomeAboutIPOModel,
    this.isExpandAboutVPBS = true
  });

  IpoHomeState copyWith({
    IpoHomeAboutIPOModel? ipoHomeAboutIPOModel,
    bool? isExpandAboutVPBS
  }) {
    return IpoHomeState(
      ipoHomeAboutIPOModel: ipoHomeAboutIPOModel ?? this.ipoHomeAboutIPOModel,
      isExpandAboutVPBS: isExpandAboutVPBS ?? this.isExpandAboutVPBS
    );
  }
}