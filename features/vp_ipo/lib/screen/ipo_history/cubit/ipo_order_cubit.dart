import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/core/repository/ipo_home_repository.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/utils/utils.dart';

import 'ipo_order_state.dart';
import 'model/ipo_info_model.dart';

class IpoOrderCubit extends Cubit<IpoOrderState> {
  IpoOrderCubit()
      : super(
    IpoOrderState(),
  );
  final IpoHomeRepository _ipoHomeRepository = GetIt.instance<IpoHomeRepository>();

  loadData(dynamic ipoInfoModel) async {
    ServerTimeKeeper? serverTimeKeeper = await IpoMainCubit.getTimeServer();
    if (ipoInfoModel != null && ipoInfoModel is IpoInfoModel) {
      update(ipoInfoModel: ipoInfoModel);
    } else {
      BaseResponse<List<IpoInfoModel>> resultInfo = await _ipoHomeRepository.ipoInfo();
      if (resultInfo.data != null && resultInfo.data!.isNotEmpty) {
        IpoInfoModel ipoInfoModel = resultInfo.data!.first;
        IpoMainCubit.mapDataForPeriodIPO(ipoInfoModel, serverTimeKeeper?.current ?? DateTime.now());
        update(ipoInfoModel: ipoInfoModel);
      }
    }
  }

  void update({double? inputOrder, String? errorInputText,
    IpoInfoModel? ipoInfoModel, PaymentType? paymentType}) {
    emit(state.copyWith(inputOrder: inputOrder, errorInputText: errorInputText,
      ipoInfoModel: ipoInfoModel,
      paymentType: paymentType
    ));
  }

  updateInputOrder({bool? isMinus, String? inputKeyboard}) {
    var defaultAmount = state.ipoInfoModel?.quantityMin ?? 100;
    var inputOrder = 0.0;
    var errorText = "";
    if (inputKeyboard != null && inputKeyboard.isNotEmpty) {
      inputKeyboard = inputKeyboard.replaceAll(",", "");
      inputOrder = double.parse(inputKeyboard);
    } else if (isMinus != null) {
      if (isMinus) {
        inputOrder = (state.inputOrder - defaultAmount) < 100 ? 0 : (state.inputOrder - defaultAmount);
      } else {
        if (state.inputOrder >= (state.ipoInfoModel?.quantityMax ?? 10000000000)) { //TODO: max fake
          inputOrder = state.ipoInfoModel?.quantityMax?.toDouble() ?? 10000000000;
        } else {
          inputOrder = state.inputOrder + defaultAmount;
        }
      }
    }

    if (inputOrder % defaultAmount != 0) {
      errorText = "Khối lượng phải là bội số của $defaultAmount";
    } else if (inputOrder > (state.ipoInfoModel?.quantityMax ?? 10000000000)) {
      errorText = "Vượt quá KL tối đa theo quy định";
    } else if (isMinus != null && !isMinus && inputOrder <= 0) {
      errorText = "Khối lượng không hợp lệ";
    }
    update(inputOrder: inputOrder, errorInputText: errorText);
  }
}
