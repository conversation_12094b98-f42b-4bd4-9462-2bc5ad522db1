import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_info_model.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_order_model.dart';

enum PaymentType { tenPercent, all }

class IpoOrderState {
  double inputOrder = 0;
  String errorInputText = "";
  IpoInfoModel? ipoInfoModel;
  PaymentType paymentType = PaymentType.tenPercent;

  IpoOrderState({
    this.inputOrder = 0,
    this.errorInputText = "",
    this.ipoInfoModel,
    this.paymentType = PaymentType.tenPercent,
  });

  IpoOrderState copyWith({
    double? inputOrder,
    String? errorInputText,
    IpoInfoModel? ipoInfoModel,
    PaymentType? paymentType,
  }) {
    return IpoOrderState(
      inputOrder: inputOrder ?? this.inputOrder,
      errorInputText: errorInputText ?? this.errorInputText,
      ipoInfoModel: ipoInfoModel ?? this.ipoInfoModel,
      paymentType: paymentType ?? this.paymentType,
    );
  }
}

extension IpoOrderStateEx on IpoOrderState {
  // String get totalMoney {
    
  // }
}

extension IpoOrderModelFilter on List<IpoOrderModel> {
  /// Filter by StatusOrder
  List<IpoOrderModel> whereStatusOrder(IPOStatusType status) {
    return where((e) => e.statusEnum == status).toList();
  }

  List<IpoOrderModel> whereStatus({
    IPOStatusType? excludeOrderStatus,
    List<IPOStatusType>? multiOrderStatus,
  }) {
    return where((e) {
      final orderOkMul =
          multiOrderStatus == null || multiOrderStatus.contains(e.statusEnum);
      final excludeOrderOk =
          excludeOrderStatus == null || e.statusEnum != excludeOrderStatus;

      return orderOkMul && excludeOrderOk;
    }).toList();
  }

  int sumQuantity() {
    return fold<int>(0, (sum, item) => sum + (item.quantity ?? 0));
  }

  double sumQuantityAllo() {
    return fold<double>(0.0, (sum, item) => sum + (item.quantityAllo ?? 0.0));
  }

  double sumValueAllo() {
    return fold<double>(0.0, (sum, item) => sum + (item.valueAllo ?? 0.0));
  }

  double sumDeposit() {
    return fold<double>(0.0, (sum, item) {
      return sum + ((item.quantity ?? 0.0) *
          (item.price ?? 0.0) * item.getDPPer());
    });
  }

  double sumTotalPayment() {
    return fold<double>(0.0, (sum, item) => sum + (item.totalPayment ?? 0.0));
  }

  List<List<IpoOrderModel>> groupByTransactionDate() {
    // 1. Group by date part of transactionDate
    final Map<DateTime, List<IpoOrderModel>> grouped = {};

    for (final order in this) {
      if (order.transactionDateParsed == null) continue;

      // keep only Y/M/D
      final dateOnly = DateTime(
        order.transactionDateParsed!.year,
        order.transactionDateParsed!.month,
        order.transactionDateParsed!.day,
      );

      grouped.putIfAbsent(dateOnly, () => []).add(order);
    }

    // 2. Sort outer keys by date descending
    final sortedKeys = grouped.keys.toList()..sort((a, b) => b.compareTo(a));

    // 3. Build final list, sort inner list by time too
    return sortedKeys.map((date) {
      final orders = grouped[date]!;
      orders.sort((a, b) => b.transactionDate!.compareTo(a.transactionDate!));
      return orders;
    }).toList();
  }
}

extension IpoOrderGroupsX on List<List<IpoOrderModel>> {
  int get totalCount => expand((e) => e).length;
}
