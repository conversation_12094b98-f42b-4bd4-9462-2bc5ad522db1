class IpoHomeAboutIPOModel {
  final bool isExpanded;
  final bool isQAExpanded;

  IpoHomeAboutIPOModel({
    this.isExpanded = true,
    this.isQAExpanded = true
  });

  IpoHomeAboutIPOModel copyWith({
    bool? isExpanded,
    bool? isQAExpanded
  }) {
    return IpoHomeAboutIPOModel(
      isExpanded: isExpanded ?? this.isExpanded,
      isQAExpanded: isQAExpanded ?? this.isQAExpanded,
    );
  }
}