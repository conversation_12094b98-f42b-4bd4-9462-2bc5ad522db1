import 'package:json_annotation/json_annotation.dart';
import 'package:vp_ipo/utils/utils.dart';

part 'ipo_info_model.g.dart';

@JsonSerializable()
class IpoInfoModel {
  final String? batch;
  final String? stType;
  final String? stockExchange;
  final String? com;
  final String? comName;
  final String? stId;
  final String? stName;
  final int? quantity;
  final double? price;
  final String? reStartDate;
  String? reEndDate;
  final String? startDate;
  final String? endDate;
  final String? status;
  final String? statusApprove;
  final double? dpPercentage;
  final double? holdRate;
  final int? volume;
  final int? quantityMin;
  final int? quantityMax;
  String? paymentStartDate;
  String? paymentEndDate;
  String? startDateAllo;
  String? endDateAllo;
  final String? chanel;
  final String? partner;

  IpoInfoModel({
    this.batch,
    this.stType,
    this.stockExchange,
    this.com,
    this.comName,
    this.stId,
    this.stName,
    this.quantity,
    this.price,
    this.reStartDate,
    this.reEndDate,
    this.startDate,
    this.endDate,
    this.status,
    this.statusApprove,
    this.dpPercentage,
    this.holdRate,
    this.volume,
    this.quantityMin,
    this.quantityMax,
    this.paymentStartDate,
    this.paymentEndDate,
    this.startDateAllo,
    this.endDateAllo,
    this.chanel,
    this.partner,
  });

  factory IpoInfoModel.fromJson(Map<String, dynamic> json) =>
      _$IpoInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$IpoInfoModelToJson(this);

  /// DateTime getters
  DateTime? get reStartDateTime => stringAnyToDate(reStartDate);
  DateTime? get reEndDateTime => stringAnyToDate(reEndDate);
  DateTime? get startDateTime => stringAnyToDate(startDate);
  DateTime? get endDateTime => stringAnyToDate(endDate);
  DateTime? get paymentStartDateTime => stringAnyToDate(paymentStartDate);
  DateTime? get paymentEndDateTime => stringAnyToDate(paymentEndDate);
  DateTime? get startDateAlloTime => stringAnyToDate(startDateAllo);
  DateTime? get endDateAlloTime => stringAnyToDate(endDateAllo);
}
