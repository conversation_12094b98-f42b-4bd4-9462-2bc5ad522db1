// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ipo_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

IpoInfoModel _$IpoInfoModelFromJson(Map<String, dynamic> json) => IpoInfoModel(
  batch: json['batch'] as String?,
  stType: json['stType'] as String?,
  stockExchange: json['stockExchange'] as String?,
  com: json['com'] as String?,
  comName: json['comName'] as String?,
  stId: json['stId'] as String?,
  stName: json['stName'] as String?,
  quantity: (json['quantity'] as num?)?.toInt(),
  price: (json['price'] as num?)?.toDouble(),
  reStartDate: json['reStartDate'] as String?,
  reEndDate: json['reEndDate'] as String?,
  startDate: json['startDate'] as String?,
  endDate: json['endDate'] as String?,
  status: json['status'] as String?,
  statusApprove: json['statusApprove'] as String?,
  dpPercentage: (json['dpPercentage'] as num?)?.toDouble(),
  holdRate: (json['holdRate'] as num?)?.toDouble(),
  volume: (json['volume'] as num?)?.toInt(),
  quantityMin: (json['quantityMin'] as num?)?.toInt(),
  quantityMax: (json['quantityMax'] as num?)?.toInt(),
  paymentStartDate: json['paymentStartDate'] as String?,
  paymentEndDate: json['paymentEndDate'] as String?,
  startDateAllo: json['startDateAllo'] as String?,
  endDateAllo: json['endDateAllo'] as String?,
  chanel: json['chanel'] as String?,
  partner: json['partner'] as String?,
);

Map<String, dynamic> _$IpoInfoModelToJson(IpoInfoModel instance) =>
    <String, dynamic>{
      'batch': instance.batch,
      'stType': instance.stType,
      'stockExchange': instance.stockExchange,
      'com': instance.com,
      'comName': instance.comName,
      'stId': instance.stId,
      'stName': instance.stName,
      'quantity': instance.quantity,
      'price': instance.price,
      'reStartDate': instance.reStartDate,
      'reEndDate': instance.reEndDate,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'status': instance.status,
      'statusApprove': instance.statusApprove,
      'dpPercentage': instance.dpPercentage,
      'holdRate': instance.holdRate,
      'volume': instance.volume,
      'quantityMin': instance.quantityMin,
      'quantityMax': instance.quantityMax,
      'paymentStartDate': instance.paymentStartDate,
      'paymentEndDate': instance.paymentEndDate,
      'startDateAllo': instance.startDateAllo,
      'endDateAllo': instance.endDateAllo,
      'chanel': instance.chanel,
      'partner': instance.partner,
    };
