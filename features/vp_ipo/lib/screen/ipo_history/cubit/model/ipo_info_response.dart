import 'package:json_annotation/json_annotation.dart';

import 'ipo_info_model.dart';

part 'ipo_info_response.g.dart';

@JsonSerializable()
class IpoInfoResponse {
  final int? status;
  final String? message;
  final List<IpoInfoModel>? data;

  IpoInfoResponse({
    this.status,
    this.message,
    this.data,
  });

  factory IpoInfoResponse.fromJson(Map<String, dynamic> json) =>
      _$IpoInfoResponseFromJson(json);

  Map<String, dynamic> toJson() => _$IpoInfoResponseToJson(this);
}
