// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ipo_info_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

IpoInfoResponse _$IpoInfoResponseFromJson(Map<String, dynamic> json) =>
    IpoInfoResponse(
      status: (json['status'] as num?)?.toInt(),
      message: json['message'] as String?,
      data:
          (json['data'] as List<dynamic>?)
              ?.map((e) => IpoInfoModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$IpoInfoResponseToJson(IpoInfoResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'data': instance.data,
    };
