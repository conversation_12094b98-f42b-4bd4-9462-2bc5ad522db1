import 'package:json_annotation/json_annotation.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/utils/utils.dart';

part 'ipo_order_model.g.dart';

@JsonSerializable(explicitToJson: true)
class IpoOrderResponse {
  final double totalPaymentAmount;
  final double totalRemAmount;
  final double totalRemPaymentAllo;
  final double refundPaymentAllo;
  final List<IpoOrderModel> data;

  IpoOrderResponse({
    required this.totalPaymentAmount,
    required this.totalRemAmount,
    required this.totalRemPaymentAllo,
    required this.refundPaymentAllo,
    required this.data,
  });

  factory IpoOrderResponse.fromJson(Map<String, dynamic> json) =>
      _$IpoOrderResponseFromJson(json);

  Map<String, dynamic> toJson() => _$IpoOrderResponseToJson(this);
}

@JsonSerializable()
class IpoOrderModel {
  final String? orderId;
  final String? accId;
  final String? custodycd;
  final String? fullName;
  final String? idNumber;
  final String? transactionType;
  final String? batch;
  final String? stId;
  final String? stName;
  final int? quantity;
    final double? price;
  final String? discountType;
  final double? discountAmount;
  final double? totalPayment;
  final double? actPayment;
  final double? remPayment;
  final double? remPaymentAllo;
  final double? refundPaymentAllo;
  final String? description;
  String? status;
  final String? channel;
  final String? statusPayment;
  String? statusAllo;

  /// raw fields from API
  final String? paymentAfAllo;
  final String? transactionDate;
  final String? createdAt;
  final String? updatedAt;
  final String? partner;
  final String? depositDueDate;
  final String? remPaymentAlloDate;
  final double? quantityAllo;
  final double? valueAllo;
  final String? actPaymentDate;
  final double? paymentAllo;
  final String? dpPer;

  // fields custom enum.
  @JsonKey(includeFromJson: false, includeToJson: false)
  PeriodIPO periodIPO = PeriodIPO.periodUnknow;


  IpoOrderModel({
    required this.orderId,
    required this.accId,
    required this.custodycd,
    this.fullName,
    required this.idNumber,
    required this.transactionType,
    required this.batch,
    required this.stId,
    this.stName,
    required this.quantity,
    required this.price,
    this.discountType,
    this.discountAmount,
    required this.totalPayment,
    required this.actPayment,
    required this.remPayment,
    this.remPaymentAllo,
    this.refundPaymentAllo,
    required this.description,
    required this.status,
    required this.channel,
    this.statusPayment,
    this.statusAllo,
    required this.transactionDate,
    this.createdAt,
    this.updatedAt,
    required this.partner,
    required this.depositDueDate,
    this.remPaymentAlloDate,
    this.quantityAllo,
    this.valueAllo,
    this.actPaymentDate,
    this.paymentAllo,
    this.dpPer,
    this.paymentAfAllo
  });

  factory IpoOrderModel.fromJson(Map<String, dynamic> json) =>
      _$IpoOrderModelFromJson(json);

  Map<String, dynamic> toJson() => _$IpoOrderModelToJson(this);

  // ✅ DateTime getters using your helper
  DateTime? get paymentAfAlloParsed => stringAnyToDate(paymentAfAllo);
  DateTime? get transactionDateParsed => stringAnyToDate(transactionDate);
  DateTime? get createdAtParsed => stringAnyToDate(createdAt);
  DateTime? get updatedAtParsed => stringAnyToDate(updatedAt);
  DateTime? get depositDueDateParsed => stringAnyToDate(depositDueDate);
  DateTime? get remPaymentAlloDateParsed => stringAnyToDate(remPaymentAlloDate);
  DateTime? get actPaymentDateParsed => stringAnyToDate(actPaymentDate);

  // -------- Enum Getters --------


  IPOStatusType get statusEnum {
    switch (status) {
      case "S0001":
        return IPOStatusType.s0001;
      case "S0002":
        return IPOStatusType.s0002;
      case "S0003_1":
        return IPOStatusType.s0003_1;
      case "S0003_2":
        return IPOStatusType.s0003_2;
      case "S0004":
        return IPOStatusType.s0004;
      case "S0005":
        return IPOStatusType.s0005;
      case "S0006":
        return IPOStatusType.s0006;
      case "S0007":
        return IPOStatusType.s0007;
      case "S0008":
        return IPOStatusType.s0008;
      default:
        return IPOStatusType.unknow;
    }
  }

  PeriodIPOHigh get periodIPOHigh {
    switch (periodIPO) {
      case PeriodIPO.periodPrepare:
      case PeriodIPO.periodRegister:
        return PeriodIPOHigh.register;
      case PeriodIPO.periodAllocate:
      case PeriodIPO.periodPayment:
        return PeriodIPOHigh.alloAndPayment;
      case PeriodIPO.periodComplete:
        return PeriodIPOHigh.complete;
      case PeriodIPO.periodUnknow:
        return PeriodIPOHigh.unknow;
    }
  }

  String get periodIPOHighString {
    switch (periodIPOHigh) {
      case PeriodIPOHigh.register:
        return "Đăng ký đặt mua";
      case PeriodIPOHigh.alloAndPayment:
        return "Phân bổ và thanh toán";
      case PeriodIPOHigh.complete:
        return "Hoàn thành đăng ký";
      case PeriodIPOHigh.unknow:
        return "";
    }
  }

  IPOStatusContract get ipoStatusContract {
    switch (statusEnum) {
      case IPOStatusType.s0001:
        return IPOStatusContract.waitingDeposit;
      case IPOStatusType.s0002:
        return IPOStatusContract.deposited;
      case IPOStatusType.s0003_1:
        return IPOStatusContract.waitingPayment;
      case IPOStatusType.s0003_2:
      case IPOStatusType.s0004:
        return IPOStatusContract.paymented;
      case IPOStatusType.s0005:
      case IPOStatusType.s0007:
      case IPOStatusType.s0008:
        return IPOStatusContract.complete;
      case IPOStatusType.s0006:
        return IPOStatusContract.failed;
      case IPOStatusType.unknow:
        return IPOStatusContract.unknow;
    }
  }

  String get ipoStatusContractString {
    switch (ipoStatusContract) {
      case IPOStatusContract.waitingDeposit:
        return "Chờ nộp cọc";
      case IPOStatusContract.deposited:
        return "Đã nộp cọc";
      case IPOStatusContract.waitingPayment:
        return "Chờ thanh toán";
      case IPOStatusContract.paymented:
        return "Đã thanh toán, chờ phân bổ";
      case IPOStatusContract.complete:
        return "Hoàn tất đăng ký mua";
      case IPOStatusContract.failed:
        return "Không hoàn tất đặt mua";
      case IPOStatusContract.unknow:
        return "";
    }
  }

  StatusAlloType get statusAlloEnum {
    switch (statusAllo) {
      case "Complete":
        return StatusAlloType.complete;
      case "Pending":
        return StatusAlloType.pending;
      case "Cancel":
        return StatusAlloType.cancel;
      case "Processing":
        return StatusAlloType.processing;
      default:
        return StatusAlloType.unknow;
    }
  }

  double getDPPer() {
    var percentString = dpPer ?? "0";
    double percent = percentString == "00" ? 100 :
    (double.parse(percentString) > 100 ? 100 : double.parse(percentString));
    return percent/100;
  }
}
