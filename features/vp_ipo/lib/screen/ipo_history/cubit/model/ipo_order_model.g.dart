// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ipo_order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

IpoOrderResponse _$IpoOrderResponseFromJson(Map<String, dynamic> json) =>
    IpoOrderResponse(
      totalPaymentAmount: (json['totalPaymentAmount'] as num).toDouble(),
      totalRemAmount: (json['totalRemAmount'] as num).toDouble(),
      totalRemPaymentAllo: (json['totalRemPaymentAllo'] as num).toDouble(),
      refundPaymentAllo: (json['refundPaymentAllo'] as num).toDouble(),
      data:
          (json['data'] as List<dynamic>)
              .map((e) => IpoOrderModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$IpoOrderResponseToJson(IpoOrderResponse instance) =>
    <String, dynamic>{
      'totalPaymentAmount': instance.totalPaymentAmount,
      'totalRemAmount': instance.totalRemAmount,
      'totalRemPaymentAllo': instance.totalRemPaymentAllo,
      'refundPaymentAllo': instance.refundPaymentAllo,
      'data': instance.data.map((e) => e.toJson()).toList(),
    };

IpoOrderModel _$IpoOrderModelFromJson(Map<String, dynamic> json) =>
    IpoOrderModel(
      orderId: json['orderId'] as String?,
      accId: json['accId'] as String?,
      custodycd: json['custodycd'] as String?,
      fullName: json['fullName'] as String?,
      idNumber: json['idNumber'] as String?,
      transactionType: json['transactionType'] as String?,
      batch: json['batch'] as String?,
      stId: json['stId'] as String?,
      stName: json['stName'] as String?,
      quantity: (json['quantity'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toDouble(),
      discountType: json['discountType'] as String?,
      discountAmount: (json['discountAmount'] as num?)?.toDouble(),
      totalPayment: (json['totalPayment'] as num?)?.toDouble(),
      actPayment: (json['actPayment'] as num?)?.toDouble(),
      remPayment: (json['remPayment'] as num?)?.toDouble(),
      remPaymentAllo: (json['remPaymentAllo'] as num?)?.toDouble(),
      refundPaymentAllo: (json['refundPaymentAllo'] as num?)?.toDouble(),
      description: json['description'] as String?,
      status: json['status'] as String?,
      channel: json['channel'] as String?,
      statusPayment: json['statusPayment'] as String?,
      statusAllo: json['statusAllo'] as String?,
      transactionDate: json['transactionDate'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
      partner: json['partner'] as String?,
      depositDueDate: json['depositDueDate'] as String?,
      remPaymentAlloDate: json['remPaymentAlloDate'] as String?,
      quantityAllo: (json['quantityAllo'] as num?)?.toDouble(),
      valueAllo: (json['valueAllo'] as num?)?.toDouble(),
      actPaymentDate: json['actPaymentDate'] as String?,
      paymentAllo: (json['paymentAllo'] as num?)?.toDouble(),
      dpPer: json['dpPer'] as String?,
      paymentAfAllo: json['paymentAfAllo'] as String?,
    );

Map<String, dynamic> _$IpoOrderModelToJson(IpoOrderModel instance) =>
    <String, dynamic>{
      'orderId': instance.orderId,
      'accId': instance.accId,
      'custodycd': instance.custodycd,
      'fullName': instance.fullName,
      'idNumber': instance.idNumber,
      'transactionType': instance.transactionType,
      'batch': instance.batch,
      'stId': instance.stId,
      'stName': instance.stName,
      'quantity': instance.quantity,
      'price': instance.price,
      'discountType': instance.discountType,
      'discountAmount': instance.discountAmount,
      'totalPayment': instance.totalPayment,
      'actPayment': instance.actPayment,
      'remPayment': instance.remPayment,
      'remPaymentAllo': instance.remPaymentAllo,
      'refundPaymentAllo': instance.refundPaymentAllo,
      'description': instance.description,
      'status': instance.status,
      'channel': instance.channel,
      'statusPayment': instance.statusPayment,
      'statusAllo': instance.statusAllo,
      'paymentAfAllo': instance.paymentAfAllo,
      'transactionDate': instance.transactionDate,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'partner': instance.partner,
      'depositDueDate': instance.depositDueDate,
      'remPaymentAlloDate': instance.remPaymentAlloDate,
      'quantityAllo': instance.quantityAllo,
      'valueAllo': instance.valueAllo,
      'actPaymentDate': instance.actPaymentDate,
      'paymentAllo': instance.paymentAllo,
      'dpPer': instance.dpPer,
    };
