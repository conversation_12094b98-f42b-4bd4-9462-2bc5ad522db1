import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_state.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_order_state.dart';
import 'package:vp_ipo/screen/ipo_history/views/ipo_history_item_view.dart';
import 'package:vp_ipo/screen/ipo_history/views/ipo_history_payment_view.dart';
import 'package:vp_ipo/screen/ipo_home/ipo_home_screen.dart';
import 'package:vp_ipo/utils/common_ui.dart';

import 'cubit/model/ipo_order_model.dart';

class IpoHistoryPaymentScreen extends StatelessWidget {
  final PeriodIPO periodIPO;
  final List<IpoOrderModel> listDataParent;
  const IpoHistoryPaymentScreen({super.key, required this.periodIPO, this.listDataParent = const []});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(create: (_) => IpoHistoryCubit()..initData(listDataParent),
    child: BlocBuilder<IpoHistoryCubit, IpoHistoryState>(builder: (context, state) {
      final allItems = state.listData.expand((group) => group.map(
            (e) => (order: e, isFirst: e == group.first),
      )).toList();
      return VPScaffold(
        backgroundColor: vpColor.backgroundElevationMinus1,
        appBar: VPAppBar.layer(
            leading: buildLeadingActionBar(context),
            title: "${state.selectedItemsPayment.isNotEmpty ? "(${state.selectedItemsPayment.length}) " : ""}hồ sơ đăng ký",
            backgroundColor: vpColor.backgroundElevation0,
            actions: buildRightActionBar(context, state.listData)
        ),
        body: Container(
          margin: EdgeInsets.symmetric(horizontal: 16),
          child: CustomScrollView(
            slivers: [
              const SliverToBoxAdapter(child: SizedBox(height: 16)),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                      (context, index) {
                    final item = allItems[index];
                    return IpoHistoryItemView(ipoOrderModel: item.order, isShowCheckBox: true, isShowSectionDate: item.isFirst);
                  },
                  childCount: state.listData.totalCount,
                ),
              ),
              const SliverToBoxAdapter(child: SizedBox(height: 16))
            ]
          )
        ),
        bottomNavigationBar: Container(
            height: 64 + MediaQuery.of(context).padding.bottom,
            padding : EdgeInsets.only(left: 16, right: 16,
                bottom: MediaQuery.of(context).padding.bottom
            ),
            decoration: BoxDecoration(
                color: vpColor.backgroundElevation0,
                border: Border.all(color: vpColor.backgroundElevation0.withValues(alpha: 0.1), width: 0.5),
                boxShadow: [
                  BoxShadow(
                      color: vpColor.backgroundElevation0,
                      blurRadius: 32
                  )
                ]
            ),
            child: IpoHistoryPaymentView()
          )
      );
    }));
  }

  buildRightActionBar(BuildContext context, List<List<IpoOrderModel>> listDataFilter) {
    return [
      InkWellNoColor(
        onTap: () {
          context.read<IpoHistoryCubit>().toggleAll();
        },
        child: Padding(
          padding: EdgeInsets.only(right: 16),
          child: Text(
            "Chọn tất cả",
            style: vpTextStyle.subtitle14.copyColor(vpColor.textBrand),
          ),
        ),
      ),
    ];
  }

  buildLeadingActionBar(BuildContext context) {
    return IconButton(
      onPressed: () {
        Navigator.pop(context);
      },
      icon: DesignAssets.icons.appbar.icClose.svg(
        colorFilter: getColorFilter(vpColor.iconPrimary),
      ),
    );
  }

  buildDataForView(IpoHistoryState state) {
    // listData.add(Text(
    //   "27/09/2025",
    //   style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
    // ));
    // listData.add(const SizedBox(height: 8));
  }
}
