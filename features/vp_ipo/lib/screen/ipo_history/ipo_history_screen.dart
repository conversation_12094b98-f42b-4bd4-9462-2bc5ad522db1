import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_launching_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/router/ipo_router.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_state.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_order_state.dart';
import 'package:vp_ipo/screen/ipo_history/views/ipo_history_empty_view.dart';
import 'package:vp_ipo/screen/ipo_history/views/ipo_history_filter_bottomsheet.dart';
import 'package:vp_ipo/screen/ipo_history/views/ipo_history_item_view.dart';
import 'package:vp_ipo/screen/ipo_history/views/ipo_history_total_view.dart';
import 'package:vp_ipo/screen/ipo_home/ipo_home_screen.dart';
import 'package:vp_ipo/screen/main/widget/ipo_tabbar_view.dart';
import 'package:vp_ipo/utils/common_ui.dart';

class IpoHistoryScreen extends StatelessWidget {
  const IpoHistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(create: (_) => IpoHistoryCubit()..loadData(periodIPO: context.read<IpoMainCubit>().state.periodIPO),
      child: BlocBuilder<IpoHistoryCubit, IpoHistoryState>(
        builder: (context, state) {
          final allItems = state.listData.expand((group) => group.map(
                (e) => (order: e, isFirst: e == group.first),
          )).toList();
          return VPScaffold(
            backgroundColor: vpColor.backgroundElevationMinus1,
            appBar: VPAppBar.layer(
              leading: buildLeadingActionBar(context),
              title: buildTitlePage(),
              backgroundColor: vpColor.backgroundElevation0,
              actions: buildRightActionBar(context, state)
            ),
            body: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                IpoHistoryTotalView(),
                state.isApiError ? SizedBox(width: double.infinity, child: IpoHistoryEmptyView(cbReaload: () {
                  context.read<IpoMainCubit>().loadData();
                  context.read<IpoHistoryCubit>().loadData(filterStatus: state.filterStatus,
                    startDate: state.startDate, endDate: state.endDate,
                    periodIPO: context.read<IpoMainCubit>().state.periodIPO);
                }))
                : (state.listData.isEmpty ?
                  SizedBox(width: double.infinity, child: IpoHistoryEmptyView()) :
                  Expanded(child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: CustomScrollView(
                    slivers: [
                      const SliverToBoxAdapter(child: SizedBox(height: 16)),
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final item = allItems[index];
                            return IpoHistoryItemView(ipoOrderModel: item.order, isShowSectionDate: item.isFirst);
                          },
                          childCount: state.listData.totalCount,
                        ),
                      ),
                      const SliverToBoxAdapter(child: SizedBox(height: heightTabbar + sizeMiddleTabbar/2)),
                    ]
                  )
                )))
              ]
            ),
            bottomNavigationBar: null
          );
      }));
  }

  buildRightActionBar(BuildContext context, IpoHistoryState state) {
    IpoMainState ipoMainState = context.read<IpoMainCubit>().state;
    return [
      InkWellNoColor(
        onTap: (){
          showIpoHistoryFilter(context, context.read<IpoHistoryCubit>().state, cbResult: (start, end, filterStatus) {
            context.read<IpoHistoryCubit>().loadData(filterStatus:filterStatus, startDate: start, endDate: end, periodIPO: ipoMainState.periodIPO);
          });
        },
        child: VpIpoAssets.icons.icFilter.svg(colorFilter: getColorFilter(vpColor.iconPrimary))
      ),
      const SizedBox(width: 12),
      InkWellNoColor(
        onTap: () {
          openBrowser("https://www.vpbanks.com.vn/vpbanks-ipo");
        },
        child: VpIpoAssets.icons.icHelp.svg(colorFilter: getColorFilter(vpColor.iconPrimary))
      ),
      const SizedBox(width: 12),
      if (state.listCanPayment.isNotEmpty)
        Padding(padding: EdgeInsets.only(right: 12),
          child: InkWellNoColor(
            onTap: () {
              context.push(VPIpoRouter.ipoHistoryPayment.routeName, extra: (listData: state.listCanPayment, periodIPO: ipoMainState.periodIPO));
            },
            child: VpIpoAssets.icons.icDoubleTick.svg(colorFilter: getColorFilter(vpColor.iconPrimary)),
          ))
    ];
  }

  buildTitlePage() {
    return 'Tra cứu';
  }

  buildLeadingActionBar(BuildContext context) {
    return IconButton(
      onPressed: () {
        Navigator.pop(context);
      },
      icon: DesignAssets.icons.appbar.icBack.svg(colorFilter: getColorFilter(vpColor.iconPrimary))
    );
  }
}