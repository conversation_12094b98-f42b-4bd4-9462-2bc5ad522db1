import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/generated/assets.gen.dart';

class IpoHistoryEmptyView extends StatelessWidget {
  final Function? cbReaload;
  const IpoHistoryEmptyView({super.key, this.cbReaload});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        const SizedBox(height: 40),
        SizedBox(
          height: 120,
          width: 120,
          child: cbReaload != null ? VpIpoAssets.images.ipoHistoryError.image() : VpIpoAssets.images.ipoHistoryEmpty.image(),
        ),
        const SizedBox(height: 16),
        Text(cbReaload != null ? "Lỗi không xác định" : "<PERSON><PERSON><PERSON> <PERSON><PERSON> hồ sơ đặt mua", style: vpTextStyle.body14.copyColor(vpColor.textPrimary)),
        if (cbReaload != null)
          Padding(padding: EdgeInsets.only(top: 16, left: 32, right: 32),
            child: VPDynamicButton.primaryXSmall(text: "Tải lại trang", onTap: () {
              if (cbReaload != null) {
                cbReaload!();
              }
            }))
      ],
    );
  }
}
