import 'package:flutter/material.dart';
import 'package:vp_common/constants/common_assets.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_state.dart';
import 'package:vp_ipo/screen/ipo_home/ipo_home_screen.dart';
import 'package:vp_ipo/utils/common_ui.dart';
import 'package:vp_ipo/utils/utils.dart';

// VPPopup.bottomSheet
// AppBottomSheet
typedef ResultCallback = void Function(
    String start,
    String end,
    FilterStatus filterStatus,
    );

void showIpoHistoryFilter(
    BuildContext context, IpoHistoryState state, {
      String? content,
      String? title,
      Widget? contentView,
      ResultCallback? cbResult
    }) {
  showModalBottomSheet(
    barrierColor: themeData.overlayBottomSheet,
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return AppBottomSheet(child: IpoHistoryFilterBottomSheet(state: state, resultFilter: cbResult));
    },
  );
}

class IpoHistoryFilterBottomSheet extends StatefulWidget {
  final IpoHistoryState state;
  final ResultCallback? resultFilter;
  const IpoHistoryFilterBottomSheet({super.key, required this.state, this.resultFilter});

  @override
  State<IpoHistoryFilterBottomSheet> createState() => _IpoHistoryFilterBottomSheetState();
}

class _IpoHistoryFilterBottomSheetState extends State<IpoHistoryFilterBottomSheet> {

  String startDate = AppTimeUtils.format(
    minusMonths(DateTime.now(), 3),
    AppTimeUtilsFormat.dateNormal,
  );
  String endDate = AppTimeUtils.format(
    DateTime.now(),
    AppTimeUtilsFormat.dateNormal,
  );

  List<FilterStatus> listFilter = [
    filterDefault,
    FilterStatus(1, "Chờ nộp cọc", [IPOStatusType.s0001]),
    FilterStatus(2, "Đã nộp cọc", [IPOStatusType.s0002]),
    FilterStatus(3, "Chờ thanh toán", [IPOStatusType.s0003_1]),
    FilterStatus(4, "Đã thanh toán, chờ phân bổ", [IPOStatusType.s0003_2, IPOStatusType.s0004]),
    FilterStatus(5, "Hoàn tất đăng ký mua", [IPOStatusType.s0005, IPOStatusType.s0007, IPOStatusType.s0008]),
    FilterStatus(6, "Không hoàn tất đặt mua", [IPOStatusType.s0006])
  ];
  
  int indexSelected = 0;

  @override
  void initState() {
    super.initState();
    indexSelected = widget.state.filterStatus?.index ?? 0;
    if (widget.state.startDate != null) {
      startDate = widget.state.startDate!;
    }
    if (widget.state.endDate != null) {
      endDate = widget.state.endDate!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: buildViewContent(context)
    );
  }

  buildViewContent(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildViewCalendar(context),
          SizedBox(height: 24),
          Text(
            "Trạng thái",
            style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
          ),
          SizedBox(height: 10),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: buildListItem(indexSelected, listFilter),
          ),
          const SizedBox(height: 24),
          VPDynamicButton.primarySmall(text: "Áp dụng", onTap: () {
            if (widget.resultFilter != null) {
              widget.resultFilter!(startDate, endDate, listFilter[indexSelected]);
            }
            Navigator.of(context).pop();
          },)
        ],
      ),
    );
  }

  buildListItem(int indexSelect, List<FilterStatus> listFilter) {
    List<Widget> listWidget = [];
    for (int i = 0; i < listFilter.length; i++) {
      listWidget.add(buildItemTextSelect(indexSelected, listFilter[i]));
    }
    return listWidget;
  }

  buildViewCalendar(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Thời gian",
          style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
        ),
        SizedBox(height: 4),
        InkWellNoColor(
          onTap: () async {
            var result = await showCalendarBottomSheet(context);
            if (result != null && result is List && result.isNotEmpty) {
              if (result.first != null && result.last != null) {
                setState(() {
                  startDate = AppTimeUtils.format(
                    result.first as DateTime?,
                    AppTimeUtilsFormat.dateNormal,
                  );
                  endDate = AppTimeUtils.format(
                    result.last as DateTime?,
                    AppTimeUtilsFormat.dateNormal,
                  );
                });
              }
           }
          },
          child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: vpColor.strokeNormal, width: 1),
                borderRadius: BorderRadius.circular(8),
                color: vpColor.backgroundElevation0,
              ),
              child: Row(
                  children: [
                    CommonAssets.icons.calendar.svg(colorFilter: getColorFilter(vpColor.iconPrimary)),
                    const SizedBox(width: 8),
                    Text("$startDate - $endDate",
                      style: vpTextStyle.body16.copyColor(vpColor.textPrimary),
                    ),
                  ]
              )
          )
        )
      ],
    );
  }

  buildItemTextSelect(int indexSelect, FilterStatus filterStatus) {
    return VPChipView.dynamic(
      text: filterStatus.name,
      size: ChipSize.medium,
      style: indexSelect == filterStatus.index ? ChipStyle.selected : ChipStyle.chipDefault,
      onTap: () {
        setState(() {
          indexSelected = filterStatus.index;
        });
      },
    );
  }
}


FilterStatus filterDefault = FilterStatus(0, "Tất cả", []);
class FilterStatus {
  String name;
  int index;
  List<IPOStatusType> listStatus;
  FilterStatus(this.index, this.name, this.listStatus);
}