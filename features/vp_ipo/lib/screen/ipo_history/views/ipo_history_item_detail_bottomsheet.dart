import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_bottom_sheet.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_order_model.dart';

// VPPopup.bottomSheet
// AppBottomSheet

void showIpoItemDetailInfo(
    BuildContext context, {
      String? content,
      required IpoOrderModel ipoOrderModel,
    }) {
  showModalBottomSheet(
    barrierColor: themeData.overlayBottomSheet,
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return AppBottomSheet(
        child: IpoHistoryItemDetailBottomSheet(ipoOrderModel: ipoOrderModel)
      );
    },
  );
}

class IpoHistoryItemDetailBottomSheet extends StatelessWidget {
  final IpoOrderModel ipoOrderModel;
  const IpoHistoryItemDetailBottomSheet({super.key, required this.ipoOrderModel});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: buildViewContent(context)
    );
  }

  buildViewContent(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        buildHeaderView(),
        buildItemInfo("KL đăng ký",
          ipoOrderModel.quantity != null ?
          "${ipoOrderModel.quantity?.toFormat3()} CP" : "-"),
        buildItemInfo("Giá trị đăng ký",
          ipoOrderModel.totalPayment != null ?
          "${ipoOrderModel.totalPayment?.toFormat3()} đ" : "-"),
        buildItemInfo( "Giá trị cọc",
          (ipoOrderModel.quantity == null ||
          ipoOrderModel.price == null ||
          ipoOrderModel.dpPer == null) ? "-" :
          "${((ipoOrderModel.price ?? 0) * (ipoOrderModel.quantity ?? 0) * ipoOrderModel.getDPPer()).toFormat3()} đ"),
        buildItemInfo("Khối lượng phân bổ",
          ((ipoOrderModel.periodIPOHigh == PeriodIPOHigh.register ||
            ipoOrderModel.quantityAllo == null) ? "-" :
          "${ipoOrderModel.quantityAllo?.toFormat3()} CP")
        ),
        buildItemInfo("Giá trị phân bổ",
          (ipoOrderModel.periodIPOHigh == PeriodIPOHigh.register ||
          ipoOrderModel.periodIPOHigh == PeriodIPOHigh.complete ||
          ipoOrderModel.valueAllo == null) ? "-" :
          "${ipoOrderModel.valueAllo?.toFormat3()} đ"
        ),
        buildItemInfo("Giá trị thanh toán",
          ((ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingDeposit && ipoOrderModel.remPayment != null) ?
          "${ipoOrderModel.remPayment?.toFormat3()} đ" :
          ((ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingPayment && ipoOrderModel.remPaymentAllo != null) ?
          "${ipoOrderModel.refundPaymentAllo?.toFormat3()} đ" :
          (ipoOrderModel.ipoStatusContract == IPOStatusContract.deposited || ipoOrderModel.ipoStatusContract == IPOStatusContract.paymented) ?
            "0 đ" : "-"
          ))
        ),
        buildItemInfo("Ngày thanh toán cọc", (ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingDeposit || ipoOrderModel.transactionDate == null) ? "-" : ipoOrderModel.transactionDateParsed?.formatToDdMmYyyy()),
        buildItemInfo("Ngày thanh toán tiền",
          ((ipoOrderModel.ipoStatusContract == IPOStatusContract.paymented ||
          ipoOrderModel.ipoStatusContract == IPOStatusContract.complete) &&
          ipoOrderModel.remPaymentAlloDate != null) ? ipoOrderModel.remPaymentAlloDateParsed?.formatToDdMmYyyy() : "-"
        ),
      ],
    );
  }

  buildTextStatus(String text, Color colorBG, Color textColor) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: colorBG,
      ),
      child: Text(text, maxLines: 3, style: vpTextStyle.captionSemiBold.copyColor(textColor))
    );
  }

  buildItemInfo(String title, String? value) {
    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(title, style: vpTextStyle.body14.copyColor(vpColor.textTertiary)),
          Expanded(child: Text(value ?? "-", textAlign: TextAlign.end, style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary))),
        ],
      ),
    );
  }

  buildHeaderView() {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              RichText(text: TextSpan(
                children: [
                  TextSpan(
                    text: 'Mã hồ sơ: ',
                    style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                  ),
                  TextSpan(
                    text: ipoOrderModel.orderId ?? "-",
                    style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
                  )
                ]
              )),
              Text(
                'Ngày đăng ký: ${ipoOrderModel.transactionDate == null ? "-" : ipoOrderModel.transactionDateParsed?.formatToDdMmYyyy()}',
                style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
              ),
            ],
          ),
          const SizedBox(width: 4),
          Expanded(child: Align(
              alignment: Alignment.centerRight,
              child: buildTextStatus(ipoOrderModel.ipoStatusContractString,
                  (ipoOrderModel.ipoStatusContract == IPOStatusContract.deposited ||
                    ipoOrderModel.ipoStatusContract == IPOStatusContract.paymented) ?
                    vpColor.backgroundAccentGreen : vpColor.backgroundAccentRed,
                  (ipoOrderModel.ipoStatusContract == IPOStatusContract.deposited ||
                    ipoOrderModel.ipoStatusContract == IPOStatusContract.paymented)
                    ? vpColor.textAccentGreen
                    : vpColor.textAccentRed)
          ))
        ],
      ),
    );
  }
}
