import 'package:flutter/material.dart';
import 'package:vp_common/constants/common_assets.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vps_primitive_color.dart';
import 'package:vp_design_system/widget/button/vp_dynamic_button.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_state.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_info_model.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_order_model.dart';
import 'package:vp_ipo/screen/ipo_history/views/ipo_history_item_detail_bottomsheet.dart';
import 'package:vp_ipo/utils/common_ui.dart';

class IpoHistoryItemView extends StatelessWidget {
  final IpoOrderModel ipoOrderModel;
  final bool isShowCheckBox;
  final bool isShowSectionDate;
  const IpoHistoryItemView({super.key, required this.ipoOrderModel, this.isShowCheckBox = false, this.isShowSectionDate = false});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IpoHistoryCubit, IpoHistoryState>(
      builder: (context, state) {
        return InkWellNoColor(
          onTap: () {
            showIpoItemDetailInfo(context, ipoOrderModel: ipoOrderModel);
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isShowSectionDate)
                Padding(padding: EdgeInsets.only(bottom: 0),
                  child: Text(
                    ipoOrderModel.transactionDateParsed?.formatToDdMmYyyy() ?? "",
                    style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
                  )),
              Container(
                margin: EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: VpIpoAssets.images.bgHistoryCard.provider(),
                    fit: BoxFit.fitWidth,
                    alignment: Alignment.topCenter,
                  ),
                ),
                child: buildViewContent(context, state)
              )
            ]
          )
        );
      }
    );
  }

  buildViewContent(BuildContext context, IpoHistoryState ipoHistoryState) {
    var widthBG = MediaQuery.of(context).size.width - 16*2;
    var heightBG = 275;
    var height = 35 * heightBG/275;
    var width = 125 * MediaQuery.of(context).size.width/343;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            buildTextCode(context, ipoHistoryState, height, width, isShowCheckBox),
            Spacer(),
            buildTextStatus(context, widthBG)
          ]
        ),
        const SizedBox(height: 16),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: buildGridItem(context)
          ),
        ),
        if (isShowBtnPayment())
          Padding(padding: EdgeInsets.only(left: 16, right: 16, top: 8),
            child: VPDynamicButton.primaryXSmall(text: "Thanh toán")),
        const SizedBox(height: 16)
      ],
    );
  }

  isShowBtnPayment() {
    if (((ipoOrderModel.periodIPO == PeriodIPO.periodRegister) &&
          ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingDeposit) ||
        ((ipoOrderModel.periodIPO == PeriodIPO.periodPayment ||
            ipoOrderModel.periodIPO == PeriodIPO.periodAllocate) &&
          ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingPayment)) {
      return true;
    }

    return false;
  }

  buildGridItem(BuildContext context) {
    List<Widget> listItem = [];
    listItem.add(buildItemInfo(context, "KL đăng ký", ipoOrderModel.quantity == null ?"-" : "${ipoOrderModel.quantity?.toFormat3()} CP"));
    listItem.add(buildItemInfo(context, "Giá trị đăng ký", ipoOrderModel.totalPayment == null ? "-" : "${ipoOrderModel.totalPayment?.toFormat3()} đ"));
    if (ipoOrderModel.periodIPOHigh != PeriodIPOHigh.complete) {
      listItem.add(buildItemInfo(context, "Giá trị cọc",
          (ipoOrderModel.quantity == null ||
              ipoOrderModel.price == null ||
              ipoOrderModel.dpPer == null) ? "-" :
          "${((ipoOrderModel.price ?? 0) * (ipoOrderModel.quantity ?? 0) * ipoOrderModel.getDPPer()).toFormat3()} đ"));
      listItem.add(buildItemInfo(context, "Số tiền cần thanh toán",
          (ipoOrderModel.ipoStatusContract == IPOStatusContract.deposited ||
              ipoOrderModel.ipoStatusContract == IPOStatusContract.paymented) ? "0 đ" :
          (ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingDeposit ? "${ipoOrderModel.remPayment?.toFormat3()} đ" : (
              ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingPayment ? "${ipoOrderModel.remPaymentAllo?.toFormat3()} đ" : "-"
          ))));
    }
    if (ipoOrderModel.periodIPOHigh != PeriodIPOHigh.register) {
      listItem.add(buildItemInfo(context, "Khối lượng phân bổ",
          ((ipoOrderModel.periodIPOHigh == PeriodIPOHigh.register ||
              ipoOrderModel.quantityAllo == null) ? "-" :
          "${ipoOrderModel.quantityAllo?.toFormat3()} CP")
      ));
    }
    if (ipoOrderModel.periodIPOHigh != PeriodIPOHigh.register) {
      listItem.add(buildItemInfo(context, "Giá trị phân bổ",
          (ipoOrderModel.periodIPOHigh == PeriodIPOHigh.register ||
              ipoOrderModel.periodIPOHigh == PeriodIPOHigh.complete ||
              ipoOrderModel.valueAllo == null) ? "-" :
          "${ipoOrderModel.valueAllo?.toFormat3()} đ"
      ));
    }
    if (ipoOrderModel.periodIPOHigh == PeriodIPOHigh.complete) {
      listItem.add(buildItemInfo(context, "KL khớp",
          ((ipoOrderModel.ipoStatusContract == IPOStatusContract.failed ||
              ipoOrderModel.valueAllo == null) ? "-" :
          "${ipoOrderModel.quantityAllo?.toFormat3()} CP")
      ));
    }

    if (ipoOrderModel.periodIPOHigh != PeriodIPOHigh.complete) {
      listItem.add(buildItemInfo(context, "Hạn thanh toán",
          ((ipoOrderModel.ipoStatusContract == IPOStatusContract.paymented ||
              ipoOrderModel.ipoStatusContract == IPOStatusContract.deposited) ? "-" :
          ((ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingDeposit) ?
          (ipoOrderModel.depositDueDateParsed?.formatToDdMmYyyy() ?? "") :
          ((ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingPayment) ?
          (ipoOrderModel.paymentAfAlloParsed?.formatToDdMmYyyy() ?? "") : ""))
      )));
    }
    
    listItem.add(buildItemInfo(context, "Trạng thái", ipoOrderModel.ipoStatusContractString,
    colorTextValue: (ipoOrderModel.ipoStatusContract == IPOStatusContract.deposited ||
        ipoOrderModel.ipoStatusContract == IPOStatusContract.paymented)
        ? vpColor.textAccentGreen
        : vpColor.textAccentRed));

    return buildTwoColumnList(listItem);
  }

  List<Widget> buildTwoColumnList(List<Widget> items) {
    final widgets = <Widget>[];

    for (int i = 0; i < items.length; i += 2) {
      final item1 = items[i];
      final item2 = i + 1 < items.length ? items[i + 1] : const SizedBox();

      widgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: item1),
              Expanded(child: item2),
            ],
          ),
        ),
      );
    }

    return widgets;
  }

  buildTextCode(BuildContext context, IpoHistoryState ipoHistoryState, dynamic height, dynamic width, bool isShowMultiSelect) {
    return InkWellNoColor(
      onTap: () {
        context.read<IpoHistoryCubit>().toggle(ipoOrderModel);
      },
      child: SizedBox(
        height: height,
        width: width,
        child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (isShowMultiSelect)
                ipoHistoryState.selectedItemsPayment.contains(ipoOrderModel) ? Container(
                    margin: EdgeInsets.only(right: 8),
                    child: CommonAssets.icons.icCheckboxRect.svg()
                ) :
                Container(
                    margin: EdgeInsets.only(right: 8),
                    child: CommonAssets.icons.icCheckboxNoneRect.svg()
                ),
              Text(
                ipoOrderModel.orderId ?? "-",
                textAlign: TextAlign.center,
                style: context.textStyle.subtitle14?.copyWith(
                    color: VpsPrimitiveColor.gray900,
                    fontWeight: FontWeight.w600
                ),
              ),
            ]
        ),
      ),
    );
  }

  buildItemInfo(BuildContext context, String title, String value, {Color? colorTextValue}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: context.textStyle.captionRegular?.copyWith(
            color: VpsPrimitiveColor.gray600,
            fontWeight: FontWeight.w400
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: context.textStyle.subtitle14?.copyWith(
              color: colorTextValue ?? VpsPrimitiveColor.gray900,
              fontWeight: FontWeight.w600
          ),
        ),
      ]
    );
  }

  buildTextStatus(BuildContext context, double widthBG) {
    String textStatus = ipoOrderModel.periodIPOHighString ?? "";
    return Container(
      decoration: textStatus.isEmpty ? null : BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFF0A2F1E),
            Color(0xFF164B33),
            Color(0xFF0A2F1E)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8), // round corners
      ),
      margin: EdgeInsets.only(right: 8, top: 4),
      width: (343-178) * widthBG/343,
      height: 22,
      child: Center(child: Text(textStatus, style: vpTextStyle.captionSemiBold.copyColor(Colors.white)))
    );
  }
}
