import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_history_state.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_order_model.dart';


class IpoHistoryPaymentView extends StatelessWidget {
  final Function? cbPayment;
  const IpoHistoryPaymentView({super.key, this.cbPayment});
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IpoHistoryCubit, IpoHistoryState>(
      builder: (context, state) {
        String money = "0 đ";
        if (state.selectedItemsPayment.isNotEmpty) {
          IpoOrderModel ipoOrderModel = state.selectedItemsPayment.first;
          if (ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingDeposit) {
            money = "${(state.selectedItemsPayment.fold<double>(0, (sum, item) => sum + (item.remPayment ?? 400000))).toFormat3()} đ";
          } else if (ipoOrderModel.ipoStatusContract == IPOStatusContract.waitingPayment) {
            money = "${(state.selectedItemsPayment.fold<double>(0, (sum, item) => sum + (item.remPaymentAllo ?? 7608409582))).toFormat3()} đ";
          }
        }

        return Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("Số tiền thanh toán", style: vpTextStyle.captionRegular.copyColor(vpColor.textTertiary)),
                Text(money, style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary)),
              ],
            )),
            Expanded(child: VPDynamicButton.primarySmall(text: "Thanh Toán", onTap: () {
              if (state.selectedItemsPayment.isNotEmpty && cbPayment != null) {
                cbPayment!();
              }
            }))
          ],
        );
      }
    );
  }
}
