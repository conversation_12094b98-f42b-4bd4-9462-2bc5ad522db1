import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_order_state.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_order_model.dart';


class IpoHistoryTotalView extends StatelessWidget {
  const IpoHistoryTotalView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IpoMainCubit, IpoMainState>(
      builder: (context, state) {
        return Container(
          height: 57,
          color: vpColor.backgroundElevation0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: buildColumnItem("<PERSON><PERSON> sơ", "${state.ipoOrderResponse?.data.length ?? 0}", isShowLine: false)),
              Expanded(
                flex: 3,
                child: buildColumnItem("Tổng khối lượng", "${(state.ipoOrderResponse?.data.sumQuantity() ?? 0).toFormat3()} CP")),
              Expanded(
                flex: 4,
                child: buildColumnItem("Tổng giá trị", "${(state.ipoOrderResponse?.data.sumTotalPayment() ?? 0).toFormat3()}đ"))
            ],
          )
        );
      },
    );
  }

  buildColumnItem(String title, String value, {bool isShowLine = true}) {
    return Container(
      height: 57,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 1,
            height: 41,
            color: isShowLine ? vpColor.strokeNormal : Colors.transparent,
          ),
          Expanded(child: Container(
              padding: EdgeInsets.only(right: 16),
              constraints: const BoxConstraints(
                  minWidth: 40
              ),
              child: Center(
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(title, style: vpTextStyle.captionRegular.copyColor(vpColor.textTertiary)),
                      const SizedBox(height: 2),
                      Text(value, style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary))
                    ]
                )
              )
          ),)
        ],
      )
    );
  }
}
