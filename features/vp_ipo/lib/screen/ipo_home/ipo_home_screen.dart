import 'package:flutter/material.dart';
import 'package:vp_common/extensions/context_extensions.dart';
import 'package:vp_common/utils/app_launching_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_home_cubit.dart';
import 'package:vp_ipo/screen/ipo_home/widget/ipo_home_about_ipo_view.dart';
import 'package:vp_ipo/screen/ipo_home/widget/banner_widget.dart';
import 'package:vp_ipo/screen/ipo_home/widget/ipo_home_about_vpbs_view.dart';
import 'package:vp_ipo/screen/ipo_home/widget/ipo_home_info_view.dart';
import 'package:vp_ipo/screen/ipo_home/widget/ipo_home_order_info_view.dart';
import 'package:vp_ipo/screen/ipo_home/widget/ipo_home_timeline_view.dart';
import 'package:vp_ipo/screen/ipo_home/widget/ipo_home_warning_payment_view.dart';
import 'package:vp_ipo/screen/main/widget/ipo_tabbar_view.dart';
import 'package:vp_ipo/utils/common_ui.dart';

class IpoHomeScreen extends StatefulWidget {
  const IpoHomeScreen({super.key});

  @override
  State<IpoHomeScreen> createState() => _IpoHomeScreenState();
}

class _IpoHomeScreenState extends State<IpoHomeScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(create: (_) => IpoHomeCubit()..loadData(),
    child: Scaffold(
      backgroundColor: vpColor.backgroundElevationMinus1,
      body: Stack(
        children: [
          Image.asset(
            context.isDark ?
            VpIpoAssets.images.bgHomeBannerDarkBack.path :
            VpIpoAssets.images.bgHomeBannerLightBack.path,
            width: double.infinity,
            package: 'vp_ipo',
            fit: BoxFit.cover,
          ),
          SingleChildScrollView(
            physics: BouncingScrollPhysics(),
            child: Stack(
              children: [
                Image.asset(
                  context.isDark ?
                  VpIpoAssets.images.bgHomeBannerDarkFront.path :
                  VpIpoAssets.images.bgHomeBannerLightFront.path,
                  width: double.infinity,
                  package: 'vp_ipo',
                  fit: BoxFit.cover,
                ),
                appbarView(),
                Padding(padding:
                EdgeInsets.only(top: calTopViewInfo(), left: 16, right: 16),
                  child: Column(
                    children: [
                      IpoHomeInfoView(),
                      const SizedBox(height: 12),
                      IpoHomeTimelineView(),
                      const SizedBox(height: 12),
                      const BannerSlider(),
                      IpoHomeWarningPaymentView(),
                      IpoHomeOrderInfoView(),
                      const SizedBox(height: 12),
                      IpoHomeAboutVPBS(),
                      IpoHomeAboutIPO(),
                      const SizedBox(height: heightTabbar + sizeMiddleTabbar/2 + 48),
                    ]
                  ))
              ],
            ),
          )
        ],
      )
    ));
  }

  Widget appbarView() {
    final paddingTop = MediaQuery.of(context).padding.top;
    final bannerTop = calHeightBanner() * 44 / 261.42;
    final top = bannerTop <= paddingTop
        ? paddingTop
        : paddingTop + (bannerTop - paddingTop) / 2;
    return Container(
      margin: EdgeInsets.only(top: top),
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: DesignAssets.icons.appbar.icBack.svg(colorFilter: getColorFilter(vpColor.iconPrimary))
          ),
          Spacer(),
          InkWellNoColor(
            onTap: () {
              showHotlineBottomSheet(context);
            },
            child: VpIpoAssets.icons.icPhone.svg(colorFilter: getColorFilter(vpColor.iconPrimary)),
          ),
          const SizedBox(width: 12),
          InkWellNoColor(
            onTap: () {
              openBrowser("https://www.vpbanks.com.vn/vpbanks-ipo");
            },
            child: VpIpoAssets.icons.icHelp.svg(colorFilter: getColorFilter(vpColor.iconPrimary))
          ),
          const SizedBox(width: 16)
        ]
      ),
    );
  }

  calHeightBanner() {
    var width = 1125;
    var height = 785;
    return MediaQuery.of(context).size.width/width * height;
  }

  calTopViewInfo() {
    var topMinusBanner = calHeightBanner() * 45 / 261.42;
    return calHeightBanner() - topMinusBanner;
  }
}

class DashWidget extends StatelessWidget {
  const DashWidget({super.key, this.height = 1, this.color = Colors.black});
  final double height;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final boxWidth = constraints.constrainWidth();
        const dashWidth = 4.0;
        final dashHeight = height;
        final dashCount = (boxWidth / (2 * dashWidth)).floor();
        return Flex(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          direction: Axis.horizontal,
          children: List.generate(dashCount, (_) {
            return SizedBox(
              width: dashWidth,
              height: dashHeight,
              child: DecoratedBox(decoration: BoxDecoration(color: color)),
            );
          }),
        );
      },
    );
  }
}

ColorFilter getColorFilter(Color color) {
  return ColorFilter.mode(
    color,
    BlendMode.srcIn,
  );
}