import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart' hide AssetGenImage;
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_home/widget/dot_page_indicator.dart';

class BannerSlider extends StatefulWidget {
  const BannerSlider({super.key});

  @override
  _BannerSliderState createState() => _BannerSliderState();
}

class _BannerSliderState extends State<BannerSlider>
    with TickerProviderStateMixin {
  final _pageController = PageController();

  late List<Widget> _pages;

  late TabController _selectorController;

  Timer? _timer;

  int _currentPage = 0;

  Duration get _duration => const Duration(milliseconds: 800);

  void _onPageChanged(int index) {
    _currentPage = index;
    _selectorController.animateTo(index, duration: _duration);
  }

  void _changePage(int index) {
    if (!_pageController.hasClients) return;

    index == 0
        ? _pageController.jumpToPage(index)
        : _pageController.animateToPage(
            index,
            duration: _duration,
            curve: Curves.ease,
          );
  }

  Widget _buildImagePageItem(Image banner) {
    return InkWell(
      onTap: () {
        context.push("/ipo-main");
      },
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(8)),
        child: VpImage.fromAsset(asset: banner),
        // child: VpImage.fromNetwork(
        //   network: CachedNetworkImage(
        //     fit: BoxFit.cover,
        //     height: double.infinity,
        //     width: double.infinity,
        //     imageUrl: banner,
        //     progressIndicatorBuilder: (context, url, downloadProgress) =>
        //         const Center(child: CircularProgressIndicator()),
        //     errorWidget: (context, url, error) =>
        //         const Center(child: Icon(Icons.error)),
        //   ),
        // ),
      ),
    );
  }

  bool navigatorGameInApp(String url) {
    if (url.contains('sinhnhat3tuoi')) {
      return true;
    }
    return false;
  }

  @override
  void initState() {
    final listBanner = [
      VpIpoAssets.images.bannerStockGuru.image(),
      VpIpoAssets.images.bannerStockGuru.image(),
    ];
    _pages = listBanner.map(_buildImagePageItem).toList();

    _selectorController = TabController(length: _pages.length, vsync: this);

    _timer = _pages.length > 1
        ? Timer.periodic(const Duration(seconds: 5), (_) {
            if (_currentPage < _pages.length - 1) {
              _changePage(_currentPage + 1);
            } else {
              _changePage(0);
            }
          })
        : null;

    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    _selectorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        AspectRatio(
          aspectRatio: 5 / 2,
          child: PageView.builder(
            physics: const AlwaysScrollableScrollPhysics(),
            controller: _pageController,
            itemCount: _pages.length,
            onPageChanged: _onPageChanged,
            itemBuilder: (BuildContext context, int index) {
              return _pages[index % _pages.length];
            },
          ),
        ),
        DotPageIndicator(tabController: _selectorController),
      ],
    );
  }
}
