import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';

class IpoHomeInfoStatusView  extends StatelessWidget {
  final IpoMainState ipoMainState;
  final Function cbCountDownDone;

  const IpoHomeInfoStatusView(this.ipoMainState, this.cbCountDownDone, {super.key});

  @override
  Widget build(BuildContext context) {
    Widget view = Container();
    switch (ipoMainState.periodIPO) {
      case PeriodIPO.periodPrepare:
      case PeriodIPO.periodUnknow:
        view = Container();
        break;
      case PeriodIPO.periodAllocate:
        view = Text(
          "Đang phân bổ",
          style: vpTextStyle.captionSemiBold.copyColor(vpColor.textWhite),
        );
        break;
      case PeriodIPO.periodRegister:
        if (ipoMainState.ipoInfoModel?.reEndDateTime == null) {
          view = Container();
        } else {
          view = CountdownTimerWidget(
            targetTime: ipoMainState.ipoInfoModel!.reEndDateTime!,
            currentTime: DateTime.now(),
            cbCountDownDone: cbCountDownDone,
          );
        }
        break;
      case PeriodIPO.periodPayment:
        if (ipoMainState.ipoInfoModel?.paymentEndDateTime == null) {
          view = Container();
        } else {
          view = CountdownTimerWidget(
            targetTime: ipoMainState.ipoInfoModel!.paymentEndDateTime!,
            currentTime: DateTime.now(),
            cbCountDownDone: cbCountDownDone,
          );
        }
        break;
      case PeriodIPO.periodComplete:
        view = Text(
          "Hoàn tất, chờ niêm yết",
          style: vpTextStyle.captionSemiBold.copyColor(vpColor.textWhite),
        );
        break;
    }
    return view;
  }
}

class CountdownTimerWidget extends StatefulWidget {
  final DateTime targetTime;
  final DateTime currentTime;
  final Function cbCountDownDone;

  const CountdownTimerWidget({
    super.key,
    required this.targetTime,
    required this.currentTime,
    required this.cbCountDownDone
  });

  @override
  State<CountdownTimerWidget> createState() => _CountdownTimerWidgetState();
}

class _CountdownTimerWidgetState extends State<CountdownTimerWidget> {
  late Timer _timer;
  late DateTime _baseTime; // we advance this each second
  final ValueNotifier<Duration> _remaining = ValueNotifier(Duration.zero);

  @override
  void initState() {
    super.initState();
    _baseTime = widget.currentTime;
    _updateRemaining();
    _timer = Timer.periodic(
      const Duration(seconds: 1),
          (_) {
        _baseTime = _baseTime.add(const Duration(seconds: 1));
        _updateRemaining();
      },
    );
  }

  void _updateRemaining() {
    final diff = widget.targetTime.difference(_baseTime);
    if (diff.isNegative) {
      widget.cbCountDownDone();
      _remaining.value = Duration.zero;
      _timer.cancel();
    } else {
      _remaining.value = diff;
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    _remaining.dispose();
    super.dispose();
  }

  Widget _dayWidget(int value, String des) {
    return Row(
      children: [
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (child, anim) =>
              FadeTransition(opacity: anim, child: child),
          child: Text(
            '$value',
            key: ValueKey(value),
            style: vpTextStyle.captionSemiBold.copyColor(vpColor.textWhite),
          ),
        ),
        const SizedBox(width: 2),
        Text(
          des,
          style: vpTextStyle.caption2Medium.copyColor(vpColor.textWhite),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Duration>(
      valueListenable: _remaining,
      builder: (_, d, __) {
        final days = d.inDays;
        final hours = d.inHours % 24;
        final minutes = d.inMinutes % 60;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            _dayWidget(days, 'ngày'),
            const Spacer(),
            Text(':', style: vpTextStyle.subtitle14.copyColor(vpColor.textWhite)),
            const Spacer(),
            _dayWidget(hours, 'giờ'),
            const Spacer(),
            Text(':', style: vpTextStyle.subtitle14.copyColor(vpColor.textWhite)),
            const Spacer(),
            _dayWidget(minutes > 0 ? minutes : (_remaining.value == Duration.zero ? 0 : 1), 'phút'),
            const Spacer(),
          ],
        );
      },
    );
  }
}

