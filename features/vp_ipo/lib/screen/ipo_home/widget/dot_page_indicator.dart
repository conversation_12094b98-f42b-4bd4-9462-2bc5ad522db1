import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class DotPageIndicator extends AnimatedWidget {
  final TabController tabController;

  const DotPageIndicator({
    Key? key,
    required this.tabController,
  }) : super(key: key, listenable: tabController);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
            tabController.length,
            (index) => AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  margin: const EdgeInsets.only(right: 8),
                  height: 8,
                  width: tabController.index == index
                      ? 24
                      : 8,
                  decoration: BoxDecoration(
                    color: tabController.index == index
                        ? themeData.primary
                        : themeData.gray300,
                    borderRadius: BorderRadius.circular(4),
                  ),
                )),
      ),
    );
  }
}
