import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_launching_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_home_about_ipo_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_home_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_home_state.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_home_about_ipo_model.dart';
import 'package:vp_ipo/screen/ipo_home/widget/ipo_home_section_view.dart';
import 'package:vp_ipo/utils/common_ui.dart';

class IpoHomeAboutIPO extends StatelessWidget {
  const IpoHomeAboutIPO({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => IpoHomeAboutIPOCubit(context.read<IpoHomeCubit>()),
      child: BlocSelector<IpoHomeCubit, IpoHomeState, IpoHomeAboutIPOModel>(
        selector: (state) => state.ipoHomeAboutIPOModel,
        builder: (context, ipoHomeAboutIPOModel) {
          return _aboutIPO(context, ipoHomeAboutIPOModel);
        },
      ));
  }

  Widget _aboutIPO(BuildContext context, IpoHomeAboutIPOModel ipoHomeAboutIPOModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        IpoHomeSectionView(imgIcon: VpIpoAssets.icons.icQa.svg(),
          title: "Giới thiệu về IPO",
          isExpanded: ipoHomeAboutIPOModel.isExpanded,
          onTap: () {
            context.read<IpoHomeAboutIPOCubit>().changeExpand(!ipoHomeAboutIPOModel.isExpanded);
        }),
        if (ipoHomeAboutIPOModel.isExpanded)
          Column(
            children: [
              const SizedBox(height: 8),
              buildTextSpan(),
              const SizedBox(height: 8),
              InkWell(
                onTap: (){
                  openBrowser("https://www.vpbanks.com.vn/vpbanks-ipo");
                },
                child: Text("Xem chi tiết", style: vpTextStyle.captionMedium.copyColor(vpColor.textBrand))
              ),
            ],
          ),
        const SizedBox(height: 12),
        buildViewContract(),
        const SizedBox(height: 12),
        buildViewQA(context, ipoHomeAboutIPOModel),
        if (ipoHomeAboutIPOModel.isQAExpanded)
          buildItemQA()
      ],
    );
  }

  buildTextSpan() {
    return RichText(text: TextSpan(
      children: <TextSpan>[
        textSpan(false, "Sau 3 năm tăng trưởng bứt phá, "),
        textSpan(true, "cổ phiếu VPBankS chính thức chào bán lần đầu"),
        textSpan(false, " ra công chúng. Tính năng IPO trực tuyến trên NEO Invest hỗ trợ nhà đầu tư "),
        textSpan(true, "nắm bắt cơ hội"),
        textSpan(false, " sớm, "),
        textSpan(true, "đăng ký mua nhanh chóng"),
        textSpan(false, " và"),
        textSpan(true, " dễ dàng theo dõi"),
        textSpan(false, " mọi giao dịch ngay trên một nền tảng. "),
      ],
    ));
  }

  textSpan(bool isBold, String text) {
    return TextSpan(
        text: text,
        style: vpTextStyle.captionRegular.copyColor(isBold ? vpColor.textPrimary : vpColor.textTertiary)
    );
  }

  buildViewContract() {
    return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(child: buildViewContractChild(VpIpoAssets.icons.icContractRegister.svg(), "HD đăng ký mua", () {
            openBrowser("https://www.vpbanks.com.vn/vpbanks-ipo");
          })),
          SizedBox(width: 12),
          Expanded(child: buildViewContractChild(VpIpoAssets.icons.icDocument.svg(), "Hồ sơ chào bán", () {
            openBrowser("https://www.vpbanks.com.vn/vpbanks-ipo");
          }))
        ]
    );
  }

  buildViewContractChild(Widget image, String text, GestureTapCallback? onTap) {
    return InkWellNoColor(
      onTap: onTap,
      child: Container(
          padding: EdgeInsets.only(top: 12, left: 12, bottom: 12),
          decoration: BoxDecoration(
            color: vpColor.backgroundElevation0,
            borderRadius: const BorderRadius.all(Radius.circular(12)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                  margin: EdgeInsets.only(bottom: 12),
                  width: 28,
                  height: 28,
                  child: image
              ),
              Text(
                text,
                style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
              ),
            ],
          )
      ),
    );
  }

  buildViewQA(BuildContext context, IpoHomeAboutIPOModel ipoHomeAboutIPOModel) {
    return IpoHomeSectionView(imgIcon: VpIpoAssets.icons.icQa.svg(),
      title: "Hỏi và Đáp", isExpanded: ipoHomeAboutIPOModel.isQAExpanded,
      onTap: (){
        context.read<IpoHomeAboutIPOCubit>().changeQAExpand(!ipoHomeAboutIPOModel.isQAExpanded);
    });
  }

  buildSectionQA(String text) {
    return InkWellNoColor(
        onTap: (){
        },
        child: Container(
          margin: EdgeInsets.only(top: 12, bottom: 8),
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: vpColor.backgroundElevationMinus2,
          ),
          child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(child: Text(
                  text,
                  maxLines: 3,
                  style: vpTextStyle.captionSemiBold?.copyWith(color: vpColor.textPrimary),
                )),
                Container(
                  height: 1.3,
                  width: 13.33,
                  color: vpColor.textPrimary,
                )
              ],
            )
        )
    );
  }

  buildItemQA() {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildSectionQA("1. Ai có thể đăng ký mua cổ phiếu IPO của VPBankS?"),
          buildTextAnswer1(),
          buildSectionQA("2. Tôi có phải là Nhà đầu tư chuyên nghiệp mới được mua không?"),
          buildTextAnswer2(),
          buildSectionQA("3. Tôi đăng ký mua cổ phiếu IPO của VPBankS bằng cách nào?"),
          buildTextAnswer3(),
          buildSectionQA("4. Thời gian đăng ký mua IPO cổ phiếu VPBankS?"),
          buildTextAnswer4(),
          buildSectionQA("5. Tôi chưa có tài khoản VPBankS thì có thể đăng ký mua không?"),
          buildTextAnswer5(),
          buildSectionQA("6. Có phí giao dịch khi đăng ký mua cổ phiếu IPO VPBankS không?"),
          buildTextAnswer6(),
          buildSectionQA("7. Quy định về số lượng đăng ký mua? Tôi có được hủy, sửa hoặc đăng ký nhiều lần không?"),
          buildTextAnswer7(),
          buildSectionQA("8. Nộp Tiền Đặt Cọc như thế nào? Tối thiểu là bao nhiêu?"),
          buildTextAnswer8(),
          buildSectionQA("9. Cách tính khối lượng cổ phiếu được phân bổ?"),
          buildTextAnswer9(),
          buildSectionQA("10. Nhận thông báo kết quả phân bổ và thanh toán phần còn lại khi nào?"),
          buildTextAnswer10(),
          buildSectionQA("11. Khi nào cổ phiếu được ghi nhận vào tài khoản VPBankS?"),
          buildTextAnswer11(),
          buildSectionQA("12. Tôi có phải làm thủ tục lưu ký chứng khoán sau khi mua không?"),
          buildTextAnswer12(),
          buildSectionQA("13. Khi nào có thể giao dịch cổ phiếu VPBankS trên sàn?"),
          buildTextAnswer13(),
        ],
      ),
    );
  }
//·
  buildTextAnswer1() {
    return RichText(text: buildTextAnswerChild("Tất cả các tổ chức, cá nhân trong và ngoài nước, không thuộc đối tượng bị cấm hoặc hạn chế quyền mua theo quy định của pháp luật, Điều lệ VPBankS và Phương án phát hành.", vpColor.textTertiary));
  }

  buildTextAnswer2() {
    return RichText(text: TextSpan(children: [
      buildTextAnswerChild("Không. Khách hàng ", vpColor.textTertiary),
      buildTextAnswerChild("không bắt buộc", vpColor.textPrimary),
      buildTextAnswerChild(" phải là Nhà đầu tư chuyên nghiệp để đăng ký mua cổ phiếu IPO của VPBankS.", vpColor.textTertiary)
    ]));
  }

  buildTextAnswer3() {
    return RichText(text: TextSpan(children: [
      buildTextAnswerChild("Bạn có thể đăng ký mua ", vpColor.textTertiary),
      buildTextAnswerChild("100% online", vpColor.textPrimary),
      buildTextAnswerChild(" thông qua ", vpColor.textTertiary),
      buildTextAnswerChild("tính năng iPO", vpColor.textPrimary),
      buildTextAnswerChild(" trên hệ thống giao dịch ", vpColor.textTertiary),
      buildTextAnswerChild("NEO Invest.\n", vpColor.textPrimary),
      buildTextAnswerChild("Tham khảo ", vpColor.textTertiary),
      buildTextAnswerChild("hướng dẫn đăng ký ", vpColor.textPrimary),
      buildTextAnswerChild("tại đây.", vpColor.textBrand, isUnderline: true, onTap: () {
        //TODO: tap
      })
    ]));
  }

  Widget buildTextAnswer4() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(text: TextSpan(children: [
          buildTextAnswerChild("Thời gian ", vpColor.textTertiary),
          buildTextAnswerChild("đăng ký mua ", vpColor.textPrimary),
          buildTextAnswerChild("và ", vpColor.textTertiary),
          buildTextAnswerChild("nộp Tiền Đặt Cọc ", vpColor.textPrimary),
          buildTextAnswerChild("dự kiến:", vpColor.textTertiary)
        ])),

        SizedBox(height: 6),
        buildBulletText([
          buildTextAnswerChild("Bắt đầu: ", vpColor.textPrimary),
          buildTextAnswerChild("8h00 ngày ", vpColor.textTertiary),
          buildTextAnswerChild("19/08/2025", vpColor.textPrimary),
        ]),

        buildBulletText([
          buildTextAnswerChild("Kết thúc: ", vpColor.textPrimary),
          buildTextAnswerChild("16h00 ngày ", vpColor.textTertiary),
          buildTextAnswerChild("08/09/2025", vpColor.textPrimary),
        ]),
      ]
    );
  }

  buildTextAnswer5() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            children: [
              // Line 1
              buildTextAnswerChild("Bạn ", vpColor.textTertiary),
              buildTextAnswerChild("cần có tài khoản chứng khoán VPBankS ", vpColor.textPrimary),
              buildTextAnswerChild("để đăng ký mua.\n", vpColor.textTertiary),

              // Line 2
              buildTextAnswerChild("Đừng lo nếu chưa có tài khoản! ", vpColor.textPrimary),
              buildTextAnswerChild("Chỉ mất ", vpColor.textTertiary),
              buildTextAnswerChild("1 phút ", vpColor.textPrimary),
              buildTextAnswerChild("để mở tài khoản ", vpColor.textTertiary),
              buildTextAnswerChild("trực tuyến ", vpColor.textPrimary),
              buildTextAnswerChild("và hoàn tất ngay trên hệ thống:", vpColor.textTertiary),
            ],
          ),
        ),
        buildBulletText([
          buildTextAnswerChild("Nhà đầu tư cá nhân: ", vpColor.textPrimary),
          buildTextAnswerChild("xem hướng dẫn tại đây", vpColor.textBrand, isUnderline: true, onTap: () {
            // TODO: handle link tap
          }),
        ]),
        buildBulletText([
          buildTextAnswerChild("Nhà đầu tư tổ chức: ", vpColor.textPrimary),
          buildTextAnswerChild("xem hướng dẫn tại đây", vpColor.textBrand, isUnderline: true, onTap: () {
            // TODO: handle link tap
          }),
        ])
      ],
    );
  }

  buildTextAnswer6() {
    return RichText(
      text: TextSpan(
        children: [
          buildTextAnswerChild("Không. Tuy nhiên, khi đăng ký mua cổ phiếu IPO của VPBankS, ", vpColor.textTertiary),
          buildTextAnswerChild("nhà đầu tư cần nộp Tiền Đặt Cọc bằng 10% tổng giá trị đăng ký ", vpColor.textPrimary),
          buildTextAnswerChild("để ghi nhận thành công.", vpColor.textTertiary),
        ],
      ),
    );
  }

  buildTextAnswer7() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        buildBulletText([
          buildTextAnswerChild("Số lượng tối thiểu: ", vpColor.textPrimary),
          buildTextAnswerChild("100 cổ phiếu.", vpColor.textTertiary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Bước khối lượng: ", vpColor.textPrimary),
          buildTextAnswerChild("bội số của 100 cổ phiếu.", vpColor.textTertiary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Số lượng tối đa: ", vpColor.textPrimary),
          buildTextAnswerChild("115.565.401 cổ phiếu (thông tin tạm, sẽ cập nhật sau).", vpColor.textTertiary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Đăng ký nhiều lần: ", vpColor.textPrimary),
          buildTextAnswerChild("được phép, nhưng ", vpColor.textTertiary),
          buildTextAnswerChild("không thể sửa hoặc hủy ", vpColor.textPrimary),
          buildTextAnswerChild("lệnh đã đăng ký.", vpColor.textTertiary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Lưu ý: ", vpColor.textPrimary),
          buildTextAnswerChild("lệnh đăng ký chỉ thành công khi ", vpColor.textTertiary),
          buildTextAnswerChild("đã thanh toán Tiền Đặt Cọc 10%.", vpColor.textPrimary),
        ]),
      ],
    );
  }

  buildTextAnswer8() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildBulletText([
          buildTextAnswerChild("Chuyển ", vpColor.textTertiary),
          buildTextAnswerChild("Tiền Đặt Cọc ", vpColor.textPrimary),
          buildTextAnswerChild("vào ", vpColor.textTertiary),
          buildTextAnswerChild("tiểu khoản Thường ", vpColor.textPrimary),
          buildTextAnswerChild("trên ", vpColor.textTertiary),
          buildTextAnswerChild("tài khoản chứng khoán VPBankS.", vpColor.textPrimary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Sau đó, thực hiện đăng ký mua trên tính năng ", vpColor.textTertiary),
          buildTextAnswerChild("iPO ", vpColor.textPrimary),
          buildTextAnswerChild("trực tuyến.", vpColor.textTertiary)
        ]),
        buildBulletText([
          buildTextAnswerChild("Tiền Đặt Cọc = ", vpColor.textPrimary),
          buildTextAnswerChild("Giá chào bán × Số lượng đăng ký ", vpColor.textTertiary),
          buildTextAnswerChild("× 10%", vpColor.textPrimary),
        ]),
        SizedBox(height: 6),
        RichText(text: TextSpan(children: [buildTextAnswerChild("Ví dụ:", vpColor.textPrimary)])),
        SizedBox(height: 6),
        buildBulletText([
          buildTextAnswerChild("Giá chào bán: ", vpColor.textTertiary),
          buildTextAnswerChild("32.000 VND/cp", vpColor.textPrimary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Đăng ký mua: ", vpColor.textTertiary),
          buildTextAnswerChild("8.800 cổ phiếu", vpColor.textPrimary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Tổng giá trị đăng ký = ", vpColor.textPrimary),
          buildTextAnswerChild("32.000 × 8.800 = ", vpColor.textTertiary),
          buildTextAnswerChild("281.600.000 VND", vpColor.textPrimary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Tiền Đặt Cọc = ", vpColor.textPrimary),
          buildTextAnswerChild("10% × 281.600.000 = ", vpColor.textTertiary),
          buildTextAnswerChild("28.160.000 VND", vpColor.textPrimary),
        ]),
      ],
    );
  }

  buildTextAnswer9() {
    return Column(
      children: [
        RichText(
          text: TextSpan(
            children: [
              // Main sentence
              buildTextAnswerChild("Nếu ", vpColor.textTertiary),
              buildTextAnswerChild("tổng số cổ phiếu đăng ký hợp lệ > tổng số lượng chào bán ", vpColor.textPrimary),
              buildTextAnswerChild("(ví dụ: 231.150.000 cổ phiếu), VPBankS sẽ phân bổ theo tỷ lệ:\n", vpColor.textTertiary),

              // Formula 1
              buildTextAnswerChild("Tỷ lệ phân bổ = ", vpColor.textPrimary),
              buildTextAnswerChild("Tổng số cổ phiếu chào bán / Tổng số cổ phiếu đăng ký hợp lệ.\n", vpColor.textTertiary),

              // Formula 2
              buildTextAnswerChild("Số cổ phiếu được phân bổ = ", vpColor.textPrimary),
              buildTextAnswerChild("Số lượng đăng ký x Tỷ lệ phân bổ (làm tròn xuống).\n", vpColor.textTertiary),

              // Ví dụ
              buildTextAnswerChild("Ví dụ:\n", vpColor.textPrimary),
            ],
          ),
        ),
        SizedBox(height: 6),
        buildBulletText([
          buildTextAnswerChild("Đăng ký: ", vpColor.textTertiary),
          buildTextAnswerChild("4.500 cổ phiếu", vpColor.textPrimary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Tỷ lệ phân bổ: ", vpColor.textTertiary),
          buildTextAnswerChild("55,55%", vpColor.textPrimary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Số lượng được phân bổ = 4.500 × 55,55% ≈ ", vpColor.textTertiary),
          buildTextAnswerChild("2.499 cổ phiếu", vpColor.textPrimary),
        ]),
        SizedBox(height: 10),
      ],
    );
  }

  Widget buildTextAnswer10() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildBulletText([
          buildTextAnswerChild("Thông báo kết quả phân bổ: ", vpColor.textPrimary),
          buildTextAnswerChild("dự kiến ", vpColor.textTertiary),
          buildTextAnswerChild("10/09/2025 ", vpColor.textPrimary),
          buildTextAnswerChild("qua Email hoặc thông báo trên", vpColor.textTertiary),
          buildTextAnswerChild("NEO Invest.", vpColor.textPrimary),
        ]),
        buildBulletText([
          buildTextAnswerChild("Thanh toán phần còn lại: ", vpColor.textPrimary),
          buildTextAnswerChild("từ ", vpColor.textTertiary),
          buildTextAnswerChild("11/09/2025 ", vpColor.textPrimary),
          buildTextAnswerChild("đến muộn nhất ", vpColor.textTertiary),
          buildTextAnswerChild("16h00 ngày 29/09/2025.", vpColor.textPrimary),
        ]),
        const SizedBox(height: 8),
        // Lưu ý
        RichText(
          text: TextSpan(
            children: [
              buildTextAnswerChild("Lưu ý:\n ", vpColor.textPrimary),
              buildTextAnswerChild("Nếu không thanh toán đủ và đúng hạn, ", vpColor.textTertiary),
              buildTextAnswerChild("lệnh mua sẽ bị hủy ", vpColor.textPrimary),
              buildTextAnswerChild("và ", vpColor.textTertiary),
              buildTextAnswerChild("Tiền Đặt Cọc sẽ không được hoàn lại.", vpColor.textPrimary,),
            ],
          ),
        ),
      ],
    );
  }

  buildTextAnswer11() {
    return RichText(
      text: TextSpan(
        children: [
          buildTextAnswerChild("Cổ phiếu sẽ được ghi nhận vào ", vpColor.textTertiary),
          buildTextAnswerChild("tài khoản chứng khoán VPBankS ", vpColor.textPrimary),
          buildTextAnswerChild("của bạn ", vpColor.textTertiary),
          buildTextAnswerChild("trong vòng 05 ngày làm việc ", vpColor.textPrimary,),
          buildTextAnswerChild("kể từ khi có công văn xác nhận của ", vpColor.textTertiary),
          buildTextAnswerChild("Ủy ban Chứng khoán Nhà nước.", vpColor.textPrimary),
        ],
      ),
    );
  }

  buildTextAnswer12() {
    return RichText(
      text: TextSpan(
        children: [
          buildTextAnswerChild("Không. Khi bạn đăng ký mua qua ", vpColor.textTertiary),
          buildTextAnswerChild("NEO Invest, ", vpColor.textPrimary),
          buildTextAnswerChild("VPBankS sẽ ", vpColor.textTertiary),
          buildTextAnswerChild("tự động thực hiện thủ tục lưu ký tập trung ", vpColor.textPrimary,),
          buildTextAnswerChild("và ghi nhận cổ phiếu trực tiếp vào tài khoản của bạn.", vpColor.textTertiary),
        ],
      ),
    );
  }

  buildTextAnswer13() {
    return RichText(
      text: TextSpan(
        children: [
          buildTextAnswerChild("Dự kiến ", vpColor.textTertiary),
          buildTextAnswerChild("Quý 4/2025, ", vpColor.textPrimary),
          buildTextAnswerChild("ngay sau khi cổ phiếu được ", vpColor.textTertiary),
          buildTextAnswerChild("lưu ký tại VSDC ", vpColor.textPrimary,),
          buildTextAnswerChild("và ", vpColor.textTertiary),
          buildTextAnswerChild("niêm yết trên HOSE, ", vpColor.textPrimary),
          buildTextAnswerChild("nhà đầu tư có thể ", vpColor.textTertiary),
          buildTextAnswerChild("giao dịch bình thường.", vpColor.textPrimary),
        ],
      ),
    );
  }


  // buildTextAnswerChild("", vpColor.textTertiary),
  // buildTextAnswerChild("", vpColor.textPrimary),
  // buildTextAnswerChild("tại đây.", vpColor.textBrand, isUnderline: true, onTap: (){
  // //TODO: tap
  // })

  buildTextAnswerChild(String text, Color color, {bool isUnderline = false, Function? onTap}) {
    return TextSpan(
      text: text,
      style: vpTextStyle.captionRegular?.copyWith(color: color,
        decoration: isUnderline ? TextDecoration.underline : TextDecoration.none,
      ),
      recognizer: TapGestureRecognizer()
        ..onTap = () {
          if (onTap != null) {
            onTap();
          }
        },
    );
  }

  Widget buildBulletText(List<InlineSpan> children) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(width: 12),
        Text(
          "•",
          style: TextStyle(color: vpColor.textPrimary),
        ),
        const SizedBox(width: 6), // spacing after bullet
        Expanded(
          child: RichText(
            text: TextSpan(children: children),
          ),
        ),
      ],
    );
  }
}