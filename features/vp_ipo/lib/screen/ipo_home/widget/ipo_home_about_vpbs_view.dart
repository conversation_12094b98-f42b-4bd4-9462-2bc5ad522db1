import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_launching_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_home_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_home_state.dart';

import 'ipo_home_section_view.dart';

class IpoHomeAboutVPBS extends StatelessWidget {
  const IpoHomeAboutVPBS({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<IpoHomeCubit, IpoHomeState, bool>(
      selector: (state) => state.isExpandAboutVPBS,
      builder: (context, isExpandAboutVPBS) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            IpoHomeSectionView(
                imgIcon: VpIpoAssets.icons.icVpbs.svg(),
                title: "Giớ<PERSON> thiệu về VPBankS",
                isExpanded: isExpandAboutVPBS,
                onTap: () {
                  context.read<IpoHomeCubit>().update(isExpandAboutVPBS: !isExpandAboutVPBS);
                }
            ),
            if (isExpandAboutVPBS)
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  Text(
                    "VPBankS là thành viên chiến lược trong hệ sinh thái tài chính Ngân hàng TMCP Việt Nam Thịnh Vượng VPBank ",
                    style: vpTextStyle.captionRegular.copyColor(vpColor.textTertiary),
                  ),
                  SvgGrid2x2(),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: (){
                      openBrowser("https://www.vpbanks.com.vn/vpbanks-ipo");
                    },
                    child: Padding(padding: EdgeInsets.only(bottom: 12),
                      child: Text("Xem chi tiết", style: vpTextStyle.captionMedium.copyColor(vpColor.textBrand)))
                  )
                ],
              ),
            const SizedBox(height: 12)
          ],
        );
      });
  }
}

class SvgGrid2x2 extends StatelessWidget {
  const SvgGrid2x2({super.key});

  @override
  Widget build(BuildContext context) {
    final icons = [
      VpIpoAssets.icons.aboutVpbs1.svg(),
      VpIpoAssets.icons.aboutVpbs2.svg(),
      VpIpoAssets.icons.aboutVpbs3.svg(),
      VpIpoAssets.icons.aboutVpbs4.svg()
    ];
    Size sizeImgAbout = Size(168, 87);
    return GridView.builder(
      padding: EdgeInsets.only(top: 20),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: icons.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        childAspectRatio: sizeImgAbout.aspectRatio,
      ),
      itemBuilder: (context, i) {
        return icons[i];
      },
    );
  }
}