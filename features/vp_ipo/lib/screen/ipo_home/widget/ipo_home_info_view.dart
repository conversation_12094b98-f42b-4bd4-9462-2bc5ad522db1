import 'package:flutter/material.dart';
import 'package:vp_common/extensions/num_extensions.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vps_primitive_color.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_info_model.dart';
import 'package:vp_ipo/utils/utils.dart';

import 'count_down_widget.dart';

class IpoHomeInfoView extends StatelessWidget {
  const IpoHomeInfoView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IpoMainCubit, IpoMainState>(
      builder: (context, state) {
        return _infoIPO(context, state);
      },
    );
  }

  Widget _infoIPO(BuildContext context, IpoMainState ipoMainState) {
    var widthBG = MediaQuery.of(context).size.width - 16*2;
    var heightBG = 154*widthBG / 345;
    return SizedBox(
      width: widthBG,
      height: heightBG,
      child: Stack(
        children: [
          Positioned.fill(child: VpIpoAssets.images.cardInfo.image()),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 31 * heightBG/152,
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      buildTextCode(context, heightBG),
                      Spacer(),
                      Container(
                          decoration:
                          (ipoMainState.periodIPO == PeriodIPO.periodPrepare ||
                          ipoMainState.periodIPO == PeriodIPO.periodUnknow) ? null : BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [
                                Color(0xFF0A2F1E),
                                Color(0xFF164B33),
                                Color(0xFF0A2F1E)
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(8), // round corners
                          ),
                          margin: EdgeInsets.only(right: 8, top: 4),
                          width: (343-178) * widthBG/343,
                          height: 22,
                          child: Center(child: IpoHomeInfoStatusView(ipoMainState, () {
                            context.read<IpoMainCubit>().refreshUI();
                          }))
                      )
                    ]
                ),
              ),
              SizedBox(height: 16),
              _infoStock(ipoMainState.ipoInfoModel),
            ],
          ),
          Positioned(left: 16, right: 16, bottom: 12,
            child: buildViewBtnContinue(ipoMainState))
        ]
      )
    );
  }

  buildViewBtnContinue(IpoMainState ipoMainState) {
    Widget view = Container();
    switch (ipoMainState.periodIPO) {
      case PeriodIPO.periodPrepare:
        view = VPDynamicButton.primaryXSmall(
          text: "Sắp mở bán (${formatDateTime(ipoMainState.ipoInfoModel?.reStartDateTime, AppTimeUtilsFormat.dateNormal)})",
          backgroundColor: vpColor.backgroundElevationMinus1,
          textColor: vpColor.backgroundElevationMinus1,
        );
        break;
      case PeriodIPO.periodRegister:
        view = VpsButton.primaryXsSmall(
          title: "Đăng ký mua",
          width: double.infinity,
        );
        break;
      case PeriodIPO.periodAllocate:
      case PeriodIPO.periodComplete:
        view = VpsButton.primaryXsSmall(
          title: "Tra cứu",
          width: double.infinity,
        );
        break;
      case PeriodIPO.periodPayment:
        view = VpsButton.primaryXsSmall(
          title: "Thanh toán",
          width: double.infinity,
        );
        break;
      default:
        break;
    }
    return view;
  }

  buildTextCode(BuildContext context, dynamic heightBG) {
    var height = 31 * heightBG/152;
    var width = 130 * MediaQuery.of(context).size.width/343;
    return SizedBox(
      height: height,
      width: width,
      child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "Mã CP:",
              style: context.textStyle.subtitle14?.copyWith(
                color: VpsPrimitiveColor.gray900,
                fontWeight: FontWeight.w600
              ),
            ),
            SizedBox(width: 2),
            SizedBox(
              height: 16,
              width: 55,
              child: VpIpoAssets.images.vpx.image()
            )
          ]
      ),
    );
  }

  Widget _infoStock(IpoInfoModel? ipoInfoModel) {
    return Row(
      children: [
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Giá chào bán",
                style: vpTextStyle.captionRegular.copyColor(VpsPrimitiveColor.gray600),
              ),
              Text(
                "${(ipoInfoModel?.price ?? 0).toMoney(showSymbol: false)} đ/CP",
                style: vpTextStyle.subtitle14?.copyWith(
                  color: VpsPrimitiveColor.gray900,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "SL chào bán",
                style: vpTextStyle.body14.copyColor(VpsPrimitiveColor.gray600),
              ),
              Text(
                "${(ipoInfoModel?.quantity ?? 0).toFormat3()} CP",
                style: vpTextStyle.subtitle14?.copyWith(
                  color: VpsPrimitiveColor.gray900,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
