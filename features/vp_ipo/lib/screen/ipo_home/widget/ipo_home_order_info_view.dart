import 'package:flutter/material.dart';
import 'package:vp_common/constants/common_assets.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/router/ipo_router.dart';
import 'package:vp_ipo/utils/common_ui.dart';

class IpoHomeOrderInfoView extends StatelessWidget {
  const IpoHomeOrderInfoView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IpoMainCubit, IpoMainState>(builder: (context, state) {
      if (state.ipoHomeOrderInfoModel == null || state.ipoHomeOrderInfoModel?.titleSum == null) {
        return const SizedBox();
      } else {
        return Container(
          margin: EdgeInsets.only(top: 12),
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: vpColor.backgroundElevation0,
          ),
          child: Column(
            children: [
              Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      "Thông tin đặt mua",
                      style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
                    ),
                    Spacer(),
                    InkWellNoColor(
                      onTap: () {
                        context.read<IpoMainCubit>().changePage(TypePageIpo.history);
                      },
                      child: CommonAssets.icons.icArrowNext.svg(
                          colorFilter: ColorFilter.mode(
                            vpColor.iconPrimary,
                            BlendMode.srcIn,
                          )
                      ),
                    )
                  ]
              ),
              const SizedBox(height: 4),
              buildItem(state.ipoHomeOrderInfoModel?.titleSum, state.ipoHomeOrderInfoModel?.valueSum),
              buildItem(state.ipoHomeOrderInfoModel?.titleAllo, state.ipoHomeOrderInfoModel?.valueAllo),
              buildItem(state.ipoHomeOrderInfoModel?.titlePayment, state.ipoHomeOrderInfoModel?.valuePayment),
              const SizedBox(height: 12),
              VPDynamicButton.primaryXSmall(text: state.ipoHomeOrderInfoModel?.titleButton ?? '',
                  onTap: () {
                    var state = context.watch<IpoMainCubit>().state;
                    if (state.periodIPO == PeriodIPO.periodRegister) {
                      context.push(VPIpoRouter.ipoOrder.routeName, extra: state.ipoInfoModel);
                      IpoMainCubit.getTimeServer();
                    } else {
                      context.read<IpoMainCubit>().changePage(TypePageIpo.history);
                    }
                  })
            ],
          ),
        );
      }
    });
  }

  buildItem(String? title, String? value) {
    if (title == null) return const SizedBox();
    return Padding(padding: EdgeInsets.only(top: 8),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          title,
          style: vpTextStyle.body14.copyColor(vpColor.textTertiary),
        ),
        Expanded(child: Text(
          value ?? "",
          textAlign: TextAlign.end,
          style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
        ))
      ],
    ));
  }
}