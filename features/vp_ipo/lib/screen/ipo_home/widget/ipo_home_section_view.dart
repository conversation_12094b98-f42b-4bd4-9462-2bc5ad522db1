import 'package:flutter/material.dart';
import 'package:vp_common/constants/common_assets.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/utils/common_ui.dart';

class IpoHomeSectionView extends StatelessWidget {
  final Widget imgIcon;
  final String title;
  final bool isExpanded;
  final Function onTap;
  const IpoHomeSectionView({super.key, required this.imgIcon,
    required this.title, required this.isExpanded, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWellNoColor(
      onTap: () {
        onTap();
      },
      child: Container(
        padding: EdgeInsets.only(top: 8, bottom: 8, left: 12, right: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: vpColor.backgroundElevation0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              height: 32,
              width: 32,
              child: imgIcon,
            ),
            const SizedBox(width: 12),
            Expanded(child: Text(title, style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary))),
            Transform.rotate(
              angle: isExpanded ? 0 : (-180 * 3.1415926535 / 180),
              child: CommonAssets.icons.arrowDrop.svg(
                  color: vpColor.iconPrimary))
          ],
        ),
      ),
    );
  }
}