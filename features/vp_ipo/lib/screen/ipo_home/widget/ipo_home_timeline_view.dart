import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_info_model.dart';
import 'package:vp_ipo/screen/ipo_home/ipo_home_screen.dart';
import 'package:vp_ipo/utils/utils.dart';

class IpoHomeTimelineView extends StatelessWidget {
  const IpoHomeTimelineView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IpoMainCubit, IpoMainState>(
      builder: (context, state) {
        return _buildIPOTimeline(context, state);
      },
    );
  }

  Widget _buildIPOTimeline(BuildContext context, IpoMainState ipoMainState) {
    PeriodIPOSlide periodIPOSlideCurrent = ipoMainState.periodIPOSlide;
    return Container(
        margin: EdgeInsets.only(top: 16, bottom: 16),
        child: Column(
          children: [
            Row(
                children: [
                  Expanded(child: itemStep(PeriodIPOSlide.periodRegister, periodIPOSlideCurrent)),
                  Expanded(child: itemStep(PeriodIPOSlide.periodAllocate, periodIPOSlideCurrent)),
                  Expanded(child: itemStep(PeriodIPOSlide.periodPayment, periodIPOSlideCurrent)),
                  Expanded(child: itemStep(PeriodIPOSlide.periodComplete, periodIPOSlideCurrent))
                ]
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                itemValue(ipoMainState.ipoInfoModel, "Đăng ký", PeriodIPOSlide.periodRegister, periodIPOSlideCurrent),
                itemValue(ipoMainState.ipoInfoModel, "Phân bổ", PeriodIPOSlide.periodAllocate, periodIPOSlideCurrent),
                itemValue(ipoMainState.ipoInfoModel, "Thanh toán", PeriodIPOSlide.periodPayment, periodIPOSlideCurrent),
                itemValue(ipoMainState.ipoInfoModel, "Hoàn tất", PeriodIPOSlide.periodComplete, periodIPOSlideCurrent),
              ],
            ),
          ],
        )
    );
  }

  Widget itemValue(IpoInfoModel? ipoInfoModel, String title, PeriodIPOSlide periodIPOSlideItem, PeriodIPOSlide periodIPOSlideCurrent) {
    bool isSuccess = false;
    String value = "";
    ipoInfoModel ??= IpoInfoModel();
    switch (periodIPOSlideItem) {
      case PeriodIPOSlide.periodRegister:
        value = "${ipoInfoModel.reStartDateTime.toDateddMMNull()} - ${ipoInfoModel.reEndDateTime.toDateddMMNull()}";
        break;
      case PeriodIPOSlide.periodAllocate:
        value = "${ipoInfoModel.startDateAlloTime.toDateddMMNull()} - ${ipoInfoModel.endDateAlloTime.toDateddMMNull()}";
        break;
      case PeriodIPOSlide.periodPayment:
        value = "${ipoInfoModel.paymentStartDateTime.toDateddMMNull()} - ${ipoInfoModel.paymentEndDateTime.toDateddMMNull()}";
        break;
      case PeriodIPOSlide.periodComplete:
        value = ipoInfoModel.endDateTime.formatToDdMmYyNull();
        break;
      case PeriodIPOSlide.periodUnknow:
        break;
    }

    switch (periodIPOSlideCurrent) {
      case PeriodIPOSlide.periodComplete:
        isSuccess = true;
        break;
      case PeriodIPOSlide.periodPayment:
        if (periodIPOSlideItem != PeriodIPOSlide.periodComplete) {
          isSuccess = true;
        }
        break;
      case PeriodIPOSlide.periodAllocate:
        if (periodIPOSlideItem != PeriodIPOSlide.periodComplete &&
            periodIPOSlideItem != PeriodIPOSlide.periodPayment) {
          isSuccess = true;
        }
        break;
      case PeriodIPOSlide.periodRegister:
        if (periodIPOSlideItem != PeriodIPOSlide.periodComplete &&
            periodIPOSlideItem != PeriodIPOSlide.periodPayment &&
            periodIPOSlideItem != PeriodIPOSlide.periodAllocate) {
          isSuccess = true;
        }
        break;
      default:
        break;
    }
    return Expanded(
      child: Column(
        children: [
          Text(
            title,
            style: vpTextStyle.captionSemiBold.copyColor(
              isSuccess ? vpColor.textBrand : vpColor.textTertiary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: vpTextStyle.caption2Medium.copyColor(
              isSuccess ? vpColor.textPrimary : vpColor.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  Widget itemStep(PeriodIPOSlide periodIPOSlideItem, PeriodIPOSlide periodIPOSlideCurrent) {
    bool isSuccess = false;
    switch (periodIPOSlideCurrent) {
      case PeriodIPOSlide.periodComplete:
        isSuccess = true;
        break;
      case PeriodIPOSlide.periodPayment:
        if (periodIPOSlideItem != PeriodIPOSlide.periodComplete) {
          isSuccess = true;
        }
        break;
      case PeriodIPOSlide.periodAllocate:
        if (periodIPOSlideItem != PeriodIPOSlide.periodComplete &&
            periodIPOSlideItem != PeriodIPOSlide.periodPayment) {
          isSuccess = true;
        }
        break;
      case PeriodIPOSlide.periodRegister:
        if (periodIPOSlideItem != PeriodIPOSlide.periodComplete &&
            periodIPOSlideItem != PeriodIPOSlide.periodPayment &&
            periodIPOSlideItem != PeriodIPOSlide.periodAllocate) {
          isSuccess = true;
        }
        break;
      default:
        break;
    }
    return  Row(
      children: [
        Expanded(child: periodIPOSlideItem == PeriodIPOSlide.periodRegister ?
          const SizedBox() :
          DashWidget(color: vpColor.strokeNormal)),
        isSuccess ? VpIpoAssets.icons.icStepSelected.svg() : VpIpoAssets.icons.icStep.svg(),
        Expanded(child: periodIPOSlideItem == PeriodIPOSlide.periodComplete ?
          const SizedBox() :
          DashWidget(color: vpColor.strokeNormal)),
      ],
    );
  }
}
