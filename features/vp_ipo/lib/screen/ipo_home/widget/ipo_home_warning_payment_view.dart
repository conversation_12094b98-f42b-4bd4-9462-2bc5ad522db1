import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';

class IpoHomeWarningPaymentView extends StatelessWidget {
  const IpoHomeWarningPaymentView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IpoMainCubit, IpoMainState>(
      buildWhen: (a, b) {
        return a.typePage != b.typePage;
      },
      builder: (context, state) {
        if (state.listNeedPay.isNotEmpty) {
          return Container(
              margin: EdgeInsets.only(top: 12),
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: vpColor.backgroundAccentYellow,
              ),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: VpIpoAssets.icons.icWarning.svg(),
                    ),
                    const SizedBox(width: 8),
                    Expanded(child: Text(
                      "Bạn có (${state.listNeedPay.length}) hồ sơ cần thanh toán trước 28/9/2025",
                      style: vpTextStyle.captionMedium.copyColor(vpColor.textPrimary),
                    )),
                    VpsButton.teriatyXsSmall(title: "Thanh toán")
                  ]
              )
          );
        }
        return Container();
      },
    );
  }
}