import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_home/ipo_home_screen.dart';
import 'package:vp_ipo/utils/common_ui.dart';

class IpoOrderConfirmScreen extends StatefulWidget {
  const IpoOrderConfirmScreen({super.key});

  @override
  State<IpoOrderConfirmScreen> createState() => _IpoOrderConfirmScreenState();
}

class _IpoOrderConfirmScreenState extends State<IpoOrderConfirmScreen> {
  final TextEditingController controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: vpColor.backgroundElevationMinus1,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Stack(
            children: [
              VPAppBar.layer(
                leading: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: DesignAssets.icons.appbar.icBack.svg(
                    colorFilter: getColorFilter(vpColor.iconPrimary),
                  ),
                ),
                title: "2. Xác nhận thông tin",
                backgroundColor: vpColor.backgroundElevation0,
              ),
              Positioned(
                bottom: 0,
                left: 16,
                right: 16,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Container(
                        height: 2,
                        color: vpColor.backgroundBrand,
                      ),
                    ),
                    SizedBox(width: 2),
                    Expanded(
                      child: Container(
                        height: 2,
                        color: vpColor.backgroundBrand,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(top: 16, left: 16, right: 16),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildViewInfo("Thông tin đặt mua", [
                      buildRowText(
                        "Mã cổ phiếu",
                        "",
                        image: VpIpoAssets.images.vpx.image(),
                      ),
                      buildRowText(
                        "Giá chào bán",
                        "32,000 VNĐ/CP",
                        valueColor: vpColor.textPrimary,
                      ),
                      buildRowText(
                        "Giá chào bán",
                        "32,000 VNĐ/CP",
                        valueColor: vpColor.textBrand,
                      ),
                      buildRowText(
                        "Giá chào bán",
                        "32,000 VNĐ/CP",
                        valueColor: vpColor.textPrimary,
                      ),
                      buildRowText(
                        "Giá chào bán",
                        "32,000 VNĐ/CP",
                        valueColor: vpColor.textPrimary,
                        isShowLine: false,
                      ),
                    ]),
                    const SizedBox(height: 28),
                    buildViewInfo("Thông tin cá nhân/tổ chức", [
                      buildRowText(
                        "Giá chào bán",
                        "32,000 VNĐ/CP",
                        valueColor: vpColor.textPrimary,
                      ),
                      buildRowText(
                        "Giá chào bán",
                        "32,000 VNĐ/CP",
                        valueColor: vpColor.textPrimary,
                        isShowLine: false,
                      ),
                    ]),
                    const SizedBox(height: 37),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        padding: EdgeInsets.only(
          top: 16,
          right: 16,
          left: 16,
          bottom: 16 + MediaQuery.of(context).padding.bottom,
        ),
        color: vpColor.backgroundElevation1,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                VPCheckBoxView(
                  type: CheckBoxType.rectangle,
                  status: CheckBoxStatus.checked,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text:
                              "Tôi đã đọc, hiểu toàn bộ Tài liệu, Hồ sơ chào bán và xác nhận chấp thuận toàn bộ nội dung tại ",
                          style: vpTextStyle.body14.copyColor(
                            vpColor.textPrimary,
                          ),
                        ),
                        TextSpan(
                          text: "đây",
                          style: vpTextStyle.body14?.copyWith(
                            color: vpColor.textBrand,
                            decoration: TextDecoration.underline,
                          ),
                          recognizer:
                              TapGestureRecognizer()
                                ..onTap = () {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text("Bạn vừa nhấn vào 'đây'"),
                                    ),
                                  );
                                },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            VpsButton.primarySmall(
              title: "Xác nhận",
              disabled: false,
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }

  buildViewInfo(String title, List<Widget> listData) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
          ),
          const SizedBox(height: 12),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: listData,
          ),
        ],
      ),
    );
  }
}
