import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/router/ipo_router.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_order_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_order_state.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_info_model.dart';
import 'package:vp_ipo/screen/ipo_home/ipo_home_screen.dart';
import 'package:vp_ipo/screen/ipo_order/widget/ipo_order_source_payment_view.dart';
import 'package:vp_ipo/screen/ipo_order/widget/ipo_order_total_money_view.dart';
import 'package:vp_ipo/utils/common_ui.dart';

class IpoOrderScreen extends StatefulWidget {
  final dynamic ipoInfoModel;
  const IpoOrderScreen({super.key, this.ipoInfoModel});

  @override
  State<IpoOrderScreen> createState() => _IpoOrderScreenState();
}

class _IpoOrderScreenState extends State<IpoOrderScreen> {
  final TextEditingController controller = TextEditingController();
  final _ipoOrderCubit = IpoOrderCubit();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.text = "100";
      _ipoOrderCubit.update(inputOrder: 100);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => IpoOrderCubit()..loadData(widget.ipoInfoModel),
      child: BlocBuilder<IpoOrderCubit, IpoOrderState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: vpColor.backgroundElevationMinus1,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    VPAppBar.layer(
                      leading: IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: DesignAssets.icons.appbar.icBack.svg(
                          colorFilter: getColorFilter(vpColor.iconPrimary),
                        ),
                      ),
                      title: "1. Đăng ký và thanh toán",
                      backgroundColor: vpColor.backgroundElevation0,
                    ),
                    Positioned(
                      bottom: 0,
                      left: 16,
                      right: 16,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Container(
                              height: 2,
                              color: vpColor.backgroundBrand,
                            ),
                          ),
                          SizedBox(width: 2),
                          Expanded(
                            child: Container(
                              height: 2,
                              color: vpColor.backgroundElevation0,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: Container(
                    margin: EdgeInsets.only(top: 16, left: 16, right: 16),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          viewWarningCCCD(),
                          viewQuantityIPO(state.ipoInfoModel?.quantity),
                          const SizedBox(height: 16),
                          viewOrderQuantityIPO(context, widget.ipoInfoModel),
                          const SizedBox(height: 16),
                          viewLine(),
                          const SizedBox(height: 16),
                          IpoOrderTotalMoneyView(),
                          const SizedBox(height: 16),
                          IpoOrderSourcePaymentView(),
                          const SizedBox(height: 16),
                          viewReferral(),
                          const SizedBox(height: 16),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            bottomNavigationBar: Container(
              padding: EdgeInsets.only(
                top: 16,
                right: 16,
                left: 16,
                bottom: 16 + MediaQuery.of(context).padding.bottom,
              ),
              color: vpColor.backgroundElevation1,
              child: VpsButton.primarySmall(
                title: "Mua ngay",
                disabled: false,
                onPressed: () {
                  context.push(VPIpoRouter.ipoOrderConfirm.routeName);
                },
              ),
            ),
          );
        },
      ),
    );
  }

  viewQuantityIPO(int? quantity) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "Mã cổ phiếu",
                style: vpTextStyle.body14.copyColor(vpColor.textTertiary),
              ),
              Spacer(),
              SizedBox(height: 16, child: VpIpoAssets.images.vpx.image()),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "Phân phối",
                style: vpTextStyle.body14.copyColor(vpColor.textTertiary),
              ),
              Expanded(
                child: Text(
                  "${FormatUtils.toFormat3(quantity ?? 0)} CP",
                  textAlign: TextAlign.end,
                  style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  viewOrderQuantityIPO(BuildContext context, IpoInfoModel? ipoInfoModel) {
    String errorText = "";

    return BlocConsumer<IpoOrderCubit, IpoOrderState>(
      listenWhen:
          (prev, curr) =>
              (prev.inputOrder != curr.inputOrder ||
                  prev.errorInputText != curr.errorInputText),
      listener: (context, state) {
        controller.text = state.inputOrder.toFormat3();
        errorText = state.errorInputText;
      },
      buildWhen:
          (prev, curr) =>
              prev.errorInputText != curr.errorInputText ||
              prev.inputOrder != curr.inputOrder,
      builder: (context, state) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Đặt mua",
              style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "Giá bán",
                  style: vpTextStyle.body14.copyColor(vpColor.textTertiary),
                ),
                Expanded(
                  child: Text(
                    "${(ipoInfoModel?.price ?? 0).toFormat3()} đ/CP",
                    textAlign: TextAlign.end,
                    style: vpTextStyle.subtitle14.copyColor(
                      vpColor.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 8),
                  child: Text(
                    "Khối lượng đặt",
                    style: vpTextStyle.body14.copyColor(vpColor.textTertiary),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        height: 40,
                        decoration: BoxDecoration(
                          color:
                              errorText.isNotEmpty
                                  ? vpColor.backgroundAccentRed
                                  : vpColor.backgroundElevation0,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                errorText.isNotEmpty
                                    ? vpColor.strokeDanger
                                    : vpColor.strokeNormal,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            InkWellNoColor(
                              onTap: () {
                                context.read<IpoOrderCubit>().updateInputOrder(
                                  isMinus: true,
                                );
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                child: SizedBox(
                                  height: 24,
                                  width: 24,
                                  child: VpIpoAssets.icons.minus.svg(
                                    colorFilter: ColorFilter.mode(
                                      vpColor.iconPrimary,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: TextFormField(
                                controller: controller,
                                textAlign: TextAlign.center,
                                decoration: InputDecoration(
                                  isDense: true,
                                  border: InputBorder.none,
                                  // remove border
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                  hintText: "0",
                                ),
                                keyboardType: TextInputType.number,
                                style: vpTextStyle.body16.copyColor(
                                  vpColor.textPrimary,
                                ),
                                textInputAction: TextInputAction.done,
                                validator: (value) {
                                  return null;
                                },
                                onChanged: (value) {
                                  context
                                      .read<IpoOrderCubit>()
                                      .updateInputOrder(inputKeyboard: value);
                                },
                                onFieldSubmitted: (value) {
                                  context
                                      .read<IpoOrderCubit>()
                                      .updateInputOrder(inputKeyboard: value);
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            InkWellNoColor(
                              onTap: () {
                                context.read<IpoOrderCubit>().updateInputOrder(
                                  isMinus: false,
                                );
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                child: SizedBox(
                                  height: 24,
                                  width: 24,
                                  child: VpIpoAssets.icons.plus.svg(
                                    colorFilter: ColorFilter.mode(
                                      vpColor.iconPrimary,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (errorText.isNotEmpty)
                        Padding(
                          padding: EdgeInsets.only(top: 4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              VpIpoAssets.icons.icWarning.svg(),
                              const SizedBox(width: 6),
                              Text(
                                errorText,
                                style: vpTextStyle.captionRegular.copyColor(
                                  vpColor.textAccentRed,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  viewReferral() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Người giới thiệu:",
            style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                "116C658568",
                style: vpTextStyle.body14.copyColor(vpColor.textBrand),
              ),
              const SizedBox(width: 4),
              Text(
                "Nguyễn Tung Dùy",
                style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
              ),
            ],
          ),
        ],
      ),
    );
  }

  viewWarningCCCD() {
    final needChangeIdMode =
        GetIt.instance<AuthCubit>().verificationInfoModel?.needChangeIdMode;
    if (needChangeIdMode != null && needChangeIdMode != 0) {
      return InkWellNoColor(
        onTap: () {},
        child: Container(
          margin: EdgeInsets.only(bottom: 16),
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          color: vpColor.backgroundAccentYellow,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                height: 24,
                width: 24,
                child: CommonAssets.icons.icWarning.svg(),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Vui lòng cập nhật thông tin ngày cấp CCCD/GPKD trước khi đặt mua",
                  maxLines: 3,
                  style: vpTextStyle.captionMedium.copyColor(
                    vpColor.textPrimary,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox();
  }
}
