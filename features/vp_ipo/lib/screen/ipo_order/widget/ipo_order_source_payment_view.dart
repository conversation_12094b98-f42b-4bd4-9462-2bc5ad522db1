import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/utils/common_ui.dart';

class IpoOrderSourcePaymentView extends StatelessWidget {
  const IpoOrderSourcePaymentView({super.key});

  @override
  Widget build(BuildContext context) {
    return viewSourcePayment();
  }

  viewSourcePayment() {
    return Container(
        decoration: BoxDecoration(
          color: vpColor.backgroundElevation0,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Padding(padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text("Nguồn thanh toán:", style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary)),
                    Expanded(child: Text("Tiểu khoản thường", textAlign: TextAlign.end, style: vpTextStyle.subtitle14.copyColor(vpColor.textBrand))),
                    const SizedBox(width: 6),
                    SizedBox(
                        height: 24,
                        width: 24,
                        child: VpIpoAssets.icons.icRefresh.svg(
                          colorFilter: ColorFilter.mode(
                            vpColor.iconPrimary,
                            BlendMode.srcIn,
                          ),
                        )
                    )
                  ],
                )),
            viewLine(),
            Padding(padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("Số dư tiểu khoản", style: vpTextStyle.body14.copyColor(vpColor.textTertiary)),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text("126.000.000.000", textAlign: TextAlign.end, style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary)),
                            const SizedBox(width: 4),
                            Text("đ", style: vpTextStyle.captionRegular.copyColor(vpColor.textTertiary)),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text("Còn thiếu 20,000,000 đ", style: vpTextStyle.captionMedium.copyColor(vpColor.textAccentRed)),
                      ],
                    ),
                    Spacer(),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: vpColor.strokePrimary, width: 1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          VpIpoAssets.icons.plus.svg(
                              colorFilter: ColorFilter.mode(
                                vpColor.iconBrand,
                                BlendMode.srcIn,
                              )
                          ),
                          const SizedBox(width: 8),
                          Text("Nạp tiền", style: vpTextStyle.subtitle14.copyColor(vpColor.textBrand)),
                          const SizedBox(width: 16)
                        ],
                      ),
                    )
                  ],
                )),
          ],
        )
    );
  }
}