import 'package:flutter/material.dart';
import 'package:number_to_words_english/number_to_words_english.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/widget/checkbox/checkbox_view.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_order_cubit.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/ipo_order_state.dart';

class IpoOrderTotalMoneyView extends StatelessWidget {
  const IpoOrderTotalMoneyView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IpoOrderCubit, IpoOrderState>(
      builder: (context, state) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "Tổng tiền thanh toán",
                  style: vpTextStyle.body14.copyColor(vpColor.textTertiary),
                ),
                Expanded(
                  child: Text(
                    (state.inputOrder * (state.ipoInfoModel?.price ?? 0))
                        .toFormat3(),
                    textAlign: TextAlign.end,
                    style: vpTextStyle.subtitle16.copyColor(vpColor.textBrand),
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  "đ",
                  style: vpTextStyle.captionRegular.copyColor(
                    vpColor.textTertiary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 6),
            Text(
              "(${(state.inputOrder * (state.ipoInfoModel?.price ?? 0)).toInt().toVietnamese().toCapitalized()} đồng)",
              textAlign: TextAlign.end,
              style: vpTextStyle.body14.copyColor(vpColor.textTertiary),
            ),
            SizedBox(height: 16),
            viewSelectPayment(),
          ],
        );
      },
    );
  }

  viewSelectPayment() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Thanh toán tiền cọc",
          style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
        ),
        const SizedBox(height: 12),
        buildViewCheckBox("Đặt cọc 10%", PaymentType.tenPercent),
        const SizedBox(height: 12),
        buildViewCheckBox("Thanh toán toàn bộ", PaymentType.all),
        BlocBuilder<IpoOrderCubit, IpoOrderState>(
          buildWhen:
              (previous, current) =>
                  previous.paymentType != current.paymentType,
          builder: (contextSum, state) {
            return (state.paymentType == PaymentType.all)
                ? Container(
                  margin: EdgeInsets.only(top: 16),
                  child: VpIpoAssets.images.ipoPromotionPaymentAll.image(),
                )
                : const SizedBox();
          },
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: vpColor.backgroundElevation0,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 12),
              Text(
                "Tổng tiền đặt mua",
                style: vpTextStyle.body14.copyColor(vpColor.textTertiary),
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  BlocBuilder<IpoOrderCubit, IpoOrderState>(
                    builder: (context, state) {
                      return Text(
                        (state.inputOrder *
                                (state.ipoInfoModel?.price ?? 0) /
                                (state.paymentType == PaymentType.tenPercent
                                    ? 10
                                    : 1))
                            .toFormat3(),
                        style: vpTextStyle.headineBold6.copyColor(
                          vpColor.textBrand,
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 4),
                  Text(
                    "đ",
                    style: vpTextStyle.captionRegular.copyColor(
                      vpColor.textTertiary,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 6),
              BlocBuilder<IpoOrderCubit, IpoOrderState>(
                builder: (context, state) {
                  return Text(
                    "(${(state.inputOrder * (state.ipoInfoModel?.price ?? 0) / (state.paymentType == PaymentType.tenPercent ? 10 : 1)).toInt().toVietnamese().toCapitalized()} đồng)",
                    style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                  );
                },
              ),
              const SizedBox(height: 12),
            ],
          ),
        ),
      ],
    );
  }

  buildViewCheckBox(String text, PaymentType type) {
    return BlocBuilder<IpoOrderCubit, IpoOrderState>(
      buildWhen:
          (previous, current) => previous.paymentType != current.paymentType,
      builder: (contextSum, state) {
        return GestureDetector(
          onTap: () {
            contextSum.read<IpoOrderCubit>().update(paymentType: type);
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              VPCheckBoxView(
                type: CheckBoxType.circle,
                status:
                    state.paymentType == type
                        ? CheckBoxStatus.checked
                        : CheckBoxStatus.uncheck,
              ),
              const SizedBox(width: 8),
              Text(
                text,
                style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
              ),

              if (type == PaymentType.all)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  margin: EdgeInsets.only(left: 8),
                  decoration: BoxDecoration(
                    color: vpColor.iconAccentRed,
                    borderRadius: BorderRadius.circular(9),
                  ),
                  child: Text(
                    "Ưu đãi",
                    style: vpTextStyle.caption2SemiBold.copyColor(
                      vpColor.textWhite,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
