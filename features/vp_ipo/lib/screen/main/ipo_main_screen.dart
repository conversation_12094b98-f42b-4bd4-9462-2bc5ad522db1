import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_common/generated/assets.gen.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/router/ipo_router.dart';
import 'package:vp_ipo/screen/ipo_history/ipo_history_screen.dart';
import 'package:vp_ipo/screen/ipo_home/ipo_home_screen.dart';
import 'package:vp_ipo/screen/main/widget/ipo_bottom_widget.dart';
import 'package:vp_ipo/screen/main/widget/ipo_tabbar_view.dart';
import 'package:vp_ipo/utils/common_ui.dart';

class IpoMainScreen extends StatefulWidget {
  const IpoMainScreen({super.key});

  @override
  State<IpoMainScreen> createState() => _IpoMainScreenState();
}

class _IpoMainScreenState extends State<IpoMainScreen> {
  get initialPage => 0;

  late final PageController _pageController = PageController(
    initialPage: initialPage,
  );

  final _children = <Widget>[const IpoHomeScreen(), Container(), const IpoHistoryScreen()];

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => IpoMainCubit()..loadData(),
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: BlocConsumer<IpoMainCubit, IpoMainState>(
          buildWhen: (previous, current) => previous.periodIPO != previous.periodIPO,
          listener: (context, state) {
            if (state.typePage != TypePageIpo.order) {
              _pageController.jumpToPage(state.typePage.index);
            } else {
              if (state.periodIPO == PeriodIPO.periodPrepare) {
                showOutOfTimeDialog(context, desc: "Thời gian đăng ký IPO không hợp lệ");
              } else {
                context.push(VPIpoRouter.ipoOrder.routeName, extra: state.ipoInfoModel);
              }
            }
          },
          builder: (context, state) {
            return Stack(
              children: [
                PageView(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: _pageController,
                  children: _children,
                ),
                Positioned(left: 0, right: 0, bottom: 0, child: IpoTabbarView()),
              ],
            );
          },
        ),
      ),
    );
  }
}
