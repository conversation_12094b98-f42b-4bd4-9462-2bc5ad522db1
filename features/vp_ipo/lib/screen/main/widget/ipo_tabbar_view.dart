import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/utils/common_ui.dart';

const double heightTabbar = 60;
const double sizeMiddleTabbar = 64;
class IpoTabbarView extends StatelessWidget {
  const IpoTabbarView({super.key});

  @override
  Widget build(BuildContext context) {
    var paddingBottom = MediaQuery.of(context).padding.bottom;
    return BlocBuilder<IpoMainCubit, IpoMainState>(
      builder: (context, state) {
        return Container(
          color: Colors.transparent,
          height: heightTabbar + sizeMiddleTabbar/2 + paddingBottom,
          child: Stack(
            children: [
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: vpColor.backgroundElevation0,
                    width: 0.5,
                  ),
                ),
                borderRadius: BorderRadius.circular(4),
                boxShadow: [
                  BoxShadow(
                    color: vpColor.backgroundElevation0,
                    spreadRadius: 2,
                    blurRadius: 2,
                    offset: const Offset(0, 0.75),
                  ),
                ],
                color: vpColor.backgroundElevation0,
              ),
              margin: EdgeInsets.only(top: sizeMiddleTabbar/2),
              padding: EdgeInsets.only(bottom: paddingBottom),
              height: heightTabbar + paddingBottom,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: buildItemTabbar(
                      context,
                      TypePageIpo.main,
                      VpIpoAssets.icons.icHome.svg(
                          colorFilter: ColorFilter.mode(
                            vpColor.iconBrand,
                            BlendMode.srcIn,
                          )),
                      VpIpoAssets.icons.icHome.svg(
                          colorFilter: ColorFilter.mode(
                            vpColor.iconTertiary,
                            BlendMode.srcIn,
                          )), "Thông tin",
                      state.typePageCurrent == TypePageIpo.main ? true : false)),
                  Expanded(child: buildItemTabbar(
                      context,
                      TypePageIpo.order,
                      Container(),
                      Container(), "Đặt mua",
                      false)),
                  Expanded(child: buildItemTabbar(
                      context,
                      TypePageIpo.history,
                      VpIpoAssets.icons.icHistory.svg(
                          colorFilter: ColorFilter.mode(
                            vpColor.iconBrand,
                            BlendMode.srcIn,
                          )),
                      VpIpoAssets.icons.icHistory.svg(
                          colorFilter: ColorFilter.mode(
                            vpColor.iconTertiary,
                            BlendMode.srcIn,
                          )), "Tra cứu",
                      state.typePageCurrent == TypePageIpo.history ? true : false))
                ],
              ),
            ),
            Align(alignment: Alignment.topCenter, child: SizedBox(
              height: sizeMiddleTabbar,
              width: sizeMiddleTabbar,
              child: InkWellNoColor(
                onTap: () {
                  context.read<IpoMainCubit>().changePage(
                      TypePageIpo.order);
                },
                child: VpIpoAssets.images.imgNavOrder.image()
              )
            ))
          ],
          ),
        );
      },
    );
  }

  buildItemTabbar(BuildContext context, TypePageIpo typePageIpo, Widget imgEnable, Widget imgDisable, String text, bool isSelected) {
    return InkWellNoColor(
      onTap: () {
        context.read<IpoMainCubit>().changePage(typePageIpo);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 24, width: 24, child: isSelected ? imgEnable : imgDisable),
          const SizedBox(height: 4),
          Text(text, style: vpTextStyle.captionMedium.copyColor(isSelected ? vpColor.textBrand : vpColor.textDisabled))
        ],
      ),
    );
  }
}
