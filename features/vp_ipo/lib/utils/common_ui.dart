import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/button_bottom_sheet.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_design_system/widget/popup/vp_popup.dart';
import 'package:vp_ipo/generated/assets.gen.dart';

viewLine() {
  return Container(
    height: 1,
    width: double.infinity,
    color: vpColor.strokeNormal,
  );
}


class InkWellNoColor extends InkWell {
  const InkWellNoColor({
    super.key,
    super.child,
    super.onTap,
    super.onDoubleTap,
    super.onLongPress,
    super.onTapDown,
    super.onTapCancel,
    super.onHighlightChanged,
    super.mouseCursor,
    super.focusColor,
    super.hoverColor,
    super.borderRadius,
    super.customBorder,
    super.enableFeedback,
    super.excludeFromSemantics,
    super.focusNode,
    super.canRequestFocus,
    super.onFocusChange,
    super.autofocus = false,
    super.splashFactory,
    super.radius,
  }) : super(
    splashColor: Colors.transparent,
    highlightColor: Colors.transparent,
  );
}

Future showHotlineBottomSheet(BuildContext context) async {
  return await showModalBottomSheet(
    isDismissible: true,
    context: context,
    backgroundColor: Colors.transparent,
    builder: (context) {
      return SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: double.infinity,
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(
                  vertical: 16,
                  horizontal: 8,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: themeData.bgPopup,
                ),
                child: InkWell(
                  onTap: () async {
                    Navigator.pop(context);
                    final Uri launchUri = Uri(
                      scheme: 'tel',
                      path: AppConstants.hotline,
                    );
                    await launchUrl(launchUri);
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        VpIpoAssets.icons.icPhone.path,
                        package: 'vp_ipo',
                        colorFilter: ColorFilter.mode(
                          themeData.gray700,
                          BlendMode.srcIn,
                        ),

                      ),
                      const SizedBox(width: 16),
                      Text(
                        'Gọi Hotline ${AppConstants.hotline}',
                        style: vpTextStyle.subtitle16.copyColor(
                          themeData.gray700,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 8),
              ButtonBottomSheet(
                text: VPCommonLocalize.current.cancel,
                color: themeData.red,
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        ),
      );
    },
  );
}

buildRowText(String title, String value, {Widget? image, Color? valueColor, bool isShowLine = true}) {
  return Column(
    mainAxisAlignment: MainAxisAlignment.start,
    children: [
      const SizedBox(height: 12),
      Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(title, style: vpTextStyle.body14.copyColor(vpColor.textTertiary)),
          Expanded(child: Align(
              alignment: Alignment.centerRight,
              child: image != null ? SizedBox(width: 32, height: 32, child: image) :
              Text(value, style: vpTextStyle.subtitle14.copyColor(valueColor ?? vpColor.textPrimary))
          ))
        ],
      ),
      const SizedBox(height: 12),
      if (isShowLine)
        viewLine()
    ],
  );
}

showOutOfTimeDialog(BuildContext context,
  {String desc = "Đã hết thời gian giao dịch.\nVui lòng thực hiện lại sau"}) {
  VPPopup.oneButton(
      title: "Ngoài giờ giao dịch",
      content: desc
  ).copyWith(
      icon: VpIpoAssets.icons.icClockOver.svg()
  ).copyWith(
    button: VpsButton.primarySmall(
      title: "Đã hiểu",
      onPressed: () {
        Navigator.pop(context);
      },
    ),
  ).showDialog(context);
}