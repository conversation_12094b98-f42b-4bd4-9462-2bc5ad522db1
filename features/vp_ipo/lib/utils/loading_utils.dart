import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class LoadingUtil {
  static bool _isDialogShowing = false;

  static void showLoading() {
    if (GetIt.instance<NavigationService>().navigatorKey.currentContext ==
        null) {
      throw Exception('CurrentContext is Null');
    } else {
      _isDialogShowing = true;
      showDialog(
        context:
            GetIt.instance<NavigationService>().navigatorKey.currentContext!,
        builder: (context) => WillPopScope(
          onWillPop: () async => false,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: themeData.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const VPBankLoading(),
            ),
          ),
        ),
      );
    }
  }

  static void hideLoading() {
    if (_isDialogShowing) {
      if (GetIt.instance<NavigationService>().navigatorKey.currentContext !=
          null) {
        Navigator.of(
          GetIt.instance<NavigationService>().navigatorKey.currentContext!,
        ).pop();
        _isDialogShowing = false;
      }
    }
  }
}

extension LoadingExt on Loading {
  Future copyTradeLoading<T>(Future Function() future, {id}) =>
      loading(future, id: id, show: true, retry: true, onError: showError);
}
