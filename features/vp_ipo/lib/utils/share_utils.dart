import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_share/flutter_share.dart';
import 'package:path_provider/path_provider.dart';
import 'package:vp_common/utils/permission_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/vp_design_system.dart';

class ShareUtils {
  static Future<ByteData?> capturePng(GlobalKey globalKey) async {
    RenderRepaintBoundary boundary =
        globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 2.0);
    return await image.toByteData(format: ui.ImageByteFormat.png);
  }

  static void shareQrcode(globalKey) async {
    try {
      final isSuccess = await PermissionUtils.checkPermissionFile();
      if (isSuccess) {
        Directory? appDocDir;

        if (Platform.isAndroid) {
          appDocDir = await getExternalStorageDirectory();
        } else {
          appDocDir = await getApplicationDocumentsDirectory();
        }
        String appDocPath = appDocDir?.path ?? '';
        if (appDocPath.isEmpty) {
          return;
        }
        final nameFile = DateTime.now().millisecondsSinceEpoch;
        String pathSave = '$appDocPath/qrcode_vpbanks_$nameFile.png';
        final bytes = await capturePng(globalKey);
        File(pathSave).writeAsBytes(bytes!.buffer.asUint8List());
        FlutterShare.shareFile(title: 'Qrcode Đại sứ IPO', filePath: pathSave);
      }
    } catch (e) {
      dlog(e);
    }
  }

  static void saveImageQr(globalKey, context) async {
    final bytes = await capturePng(globalKey);
    final nameFile = DateTime.now().millisecondsSinceEpoch;
    await DownloadFileManager()
        .saveFileWithByteData(
          byteData: bytes!,
          fileName: 'qrcode_vpbanks_$nameFile',
          extension: FileExtension.png,
        )
        .then((value) => showSnackBar(context, "Tải xuống thành công"))
        .onError(
          (error, stackTrace) =>
              showSnackBar(context, "Tải xuống thất bại", isSuccess: false),
        );
  }
}
