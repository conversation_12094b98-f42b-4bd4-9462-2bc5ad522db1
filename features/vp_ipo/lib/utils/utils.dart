
import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/button_bottom_sheet.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';

DateTime minusMonths(DateTime d, int months) {
  var y = d.year;
  var m = d.month - months;

  // normalize year/month without tricky negative division
  while (m <= 0) { m += 12; y--; }
  while (m > 12) { m -= 12; y++; }

  final lastDay = DateTime(y, m + 1, 0).day; // last day of target month
  final day = d.day <= lastDay ? d.day : lastDay;

  return DateTime(y, m, day, d.hour, d.minute, d.second, d.millisecond, d.microsecond);
}

DateTime? stringAnyToDate(String? rawDate) {
  if (rawDate == null || rawDate.trim().isEmpty) return null;

  try {
    DateTime dateTime = DateTime.parse(rawDate);
    return dateTime;
  } catch (_) {}


  // List of possible date formats
  final formats = [
    'dd-MM-yyyy',
    'd-MM-yyyy',
    'yyyy-MM-dd',
    'yyyy-M-d',
    'dd/MM/yyyy',
    'd/M/yyyy',
    'MM/dd/yyyy',
    'M/d/yyyy',
    'yyyy/MM/dd',
    'yyyy/M/d',
    'yyyyMMdd',
    'yyyy-MM',
    'yyyy/MM',
    'MM-yyyy',
    'M-yyyy',
    'MM/yyyy',
    'M/yyyy',
    'yyyy',
    'dd-MM-yyyy HH:mm:ss',
    'yyyy-MM-dd HH:mm:ss',
    'dd/MM/yyyy HH:mm:ss',
    'yyyy/MM/dd HH:mm:ss',
    'yyyy-MM-ddTHH:mm:ss',
    'yyyy-MM-ddTHH:mm:ss.SSSZ',
  ];

  for (final pattern in formats) {
    try {
      final date = DateFormat(pattern).parseStrict(rawDate);
      return date;
    } catch (_) {
      continue;
    }
  }

  return null;
}

bool compareDateSameDayOrDiff(DateTime? day1, DateTime? day2,
    {bool isDayOnly = false, bool isAfter = false, bool isBefore = false}) {
  DateTime? day1Temp = day1;
  DateTime? day2Temp = day2;
  if (isDayOnly) {
    day1Temp = formatDateTime(day1, AppTimeUtilsFormat.dateConditionOrder)!;
    day2Temp = formatDateTime(day2, AppTimeUtilsFormat.dateConditionOrder);
  }

  if (isDayOnly && day1Temp != null && day1Temp.isSameDay(day2Temp)) return true;

  if (isAfter) {
    if (day1Temp != null && day2Temp != null && day1Temp.isAfter(day2Temp)) return true;
  }

  if (isBefore) {
    if (day1Temp != null && day2Temp != null && day1Temp.isBefore(day2Temp)) return true;
  }

  return false;
}

DateTime? formatDateTime(DateTime? date, String pattern) {
  if (date == null) return null;
  String formatted = DateFormat(pattern).format(date);
  return DateFormat(pattern).parse(formatted);
}

extension DateTimeExtsNull on DateTime? {
  String toDateddMMNull() {
    if (this == null) return "";
    return DateFormat('dd/MM').format(this!);
  }


  String formatToDdMmYyNull() {
    if (this == null) return "";
    return DateFormat('dd/MM/yy').format(this!);
  }
}

class ServerTimeKeeper {
  final DateTime serverTime;   // the time returned by API
  final DateTime localFetched; // the moment we fetched it

  ServerTimeKeeper(this.serverTime) : localFetched = DateTime.now();

  /// Get "current" server time at any moment
  DateTime get current {
    final diff = DateTime.now().difference(localFetched);
    return serverTime.add(diff);
  }
}