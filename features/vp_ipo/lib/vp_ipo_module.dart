import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart' hide AppLocalizationDelegate;
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/core/repository/ipo_ambassador_repository.dart';
import 'package:vp_ipo/core/repository/ipo_home_repository.dart';
import 'package:vp_ipo/core/service/ipo_ambassador_service.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/l10n.dart';
import 'package:vp_ipo/router/ipo_router.dart';
import 'package:vp_ipo/screen/ipo_ambassador/ipo_ambassador_screen.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_info_model.dart';
import 'package:vp_ipo/screen/ipo_history/cubit/model/ipo_order_model.dart';
import 'package:vp_ipo/screen/ipo_history/ipo_history_payment_screen.dart';
import 'package:vp_ipo/screen/ipo_order/ipo_order_confirm_screen.dart';
import 'package:vp_ipo/screen/ipo_order/ipo_order_screen.dart';
import 'package:vp_ipo/screen/main/ipo_main_screen.dart';

import 'core/service/ipo_home_service.dart';
import 'generated/intl/messages_all.dart';
import 'utils/url_env.dart';

class IpoModule implements Module {
  final assetShellKey = GlobalKey<NavigatorState>();

  @override
  void injectServices(GetIt service) {
    service.registerLazySingleton(() => IpoHomeService(service(), baseUrl: "https://openapi-uat.vpbanks.com.vn"));
    service.registerLazySingleton<IpoHomeRepository>(
      () => IpoHomeRepositoryImpl(ipoHomeService: service()),
    );
    service.registerLazySingleton(
      () => IpoAmbassadorService(service()),
    );
    service.registerLazySingleton<IpoAmbassadorRepository>(
      () => IpoAmbassadorRepositoryImpl(ipoAmbassadorService: service()),
    );

  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: VPIpoRouter.ipoMain.routeName,
        builder: (context, state) => IpoMainScreen(),
      ),
      GoRoute(
        path: VPIpoRouter.ipoAmbassador.routeName,
        builder: (context, state) => IpoAmbassadorScreen(),
      ),
      GoRoute(
        path: VPIpoRouter.ipoHistoryPayment.routeName,
        builder: (context, state) {
          final extra = state.extra as ({List<IpoOrderModel> listData, PeriodIPO periodIPO});
          return IpoHistoryPaymentScreen(listDataParent: extra.listData, periodIPO: extra.periodIPO);
        },
      ),
      GoRoute(
        path: VPIpoRouter.ipoOrder.routeName,
        builder: (context, state) {
          final ipoInfoModel = state.extra as dynamic;
          return IpoOrderScreen(ipoInfoModel: ipoInfoModel);
        },
      ),
      GoRoute(
          path: VPIpoRouter.ipoOrderConfirm.routeName,
          builder: (context, state) {
            return IpoOrderConfirmScreen();
          }
      ),
    ];
  }

  @override
  Map<String, PageRoute> legacyRouter(RouteSettings settings) {
    return {
      VPIpoRouter.ipoMain.routeName: MaterialPageRoute(
        builder: (_) => IpoMainScreen(),
        settings: settings,
      ),
      VPIpoRouter.ipoAmbassador.routeName: MaterialPageRoute(
        builder: (_) => IpoAmbassadorScreen(),
        settings: settings,
      ),
      VPIpoRouter.ipoOrder.routeName:  MaterialPageRoute(
        builder: (_) => IpoOrderScreen(ipoInfoModel: settings.arguments as IpoInfoModel),
        settings: settings,
      ),
    
      // GoRoute(
      //   path: VPIpoRouter.ipoHistoryPayment.routeName,
      //   builder: (context, state) {
      //     final listData = state.extra as List<IpoHistoryModel>;
      //     return IpoHistoryPaymentScreen(listData: listData);
      //   },
      // ),
      // GoRoute(
      //   path: VPIpoRouter.ipoOrder.routeName,
      //   builder: (context, state) {
      //     return IpoOrderScreen();
      //   },
      // ),
    };
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [VPIpoLocalize.delegate];
  }

  @override
  String modulePath() {
    return 'vp_ipo';
  }
}

class MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = MultiLocalizationsDelegate();

  @override
  Future<VPIpoLocalize> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => VPIpoLocalize.load(locale),
      setDefaultLocale: true,
    );
  }
}
