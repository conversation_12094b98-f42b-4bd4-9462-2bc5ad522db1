import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_market/data/chart_buy_up_sell_entity.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/transaction_classification/transaction_classification_state.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class TransactionClassificationBloc
    extends Cubit<TransactionClassificationState>
    with BuyUpSellDownSocketMixin {
  TransactionClassificationBloc({required MarketInfoModel market})
    : super(
        TransactionClassificationState(
          status: ApiStatus.loading(),
          market: market,
          typeTransaction: ItemSelect(
            id: null,
            title: VPMarketInfoLocalize.current.all,
          ),
          chartEntity: ChartBuyUpSellEntity(null),
        ),
      );

  final _repository = GetIt.instance<MarketRepository>();

  void selectTypeTransactionClassification(ItemSelect value) {
    if (value.id != state.typeTransaction) {
      emit(state.copyWith(typeTransaction: value));
    }
  }

  Future loadData() async {
    try {
      emit(state.copyWith(status: ApiStatus.loading()));

      final value = await _repository.getBuyUpSellDownByMarket(
        state.market.indexCode.value,
      );

      final chartBuyUpSellEntity = ChartBuyUpSellEntity(value);

      emit(
        state.copyWith(
          status: ApiStatus.done(),
          chartEntity: chartBuyUpSellEntity,
        ),
      );

      subscribeBuyUpSellDownMarket({state.market.indexCode.socketChannel});
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(status: ApiStatus.error(e)));
    }
  }

  @override
  void onSocketBuyUpSellDownListener(VPBuyUpSellDownMarketData data) {
    updateChart(data);
  }

  void updateChart(VPBuyUpSellDownMarketData data) {
    String? ts = '${data.timeStampS ?? ''}';
    String? tm = '${data.timeStampM ?? ''}';
    String? tl = '${data.timeStampL ?? ''}';
    ChartBuyUpSellEntity chartEntity = state.chartEntity;
    bool update = false;
    final indexS = chartEntity.sChartDataBuy.indexWhere((e) => e?.x == ts);
    final indexM = chartEntity.mChartDataBuy.indexWhere((e) => e?.x == ts);
    final indexL = chartEntity.lChartDataBuy.indexWhere((e) => e?.x == ts);
    if (ts.isNotEmpty && indexS >= 0) {
      chartEntity.sChartDataBuy[indexS] = ChartBuyUpSellData(
        ts,
        ChartBuyUpSellEntity.getYBuy(data.buyUpValueS),
      );
      chartEntity.sChartDataSell[indexS] = ChartBuyUpSellData(
        ts,
        ChartBuyUpSellEntity.getYSell(data.sellDownValueS),
      );
      update = true;
    }
    if (tm.isNotEmpty && indexM >= 0) {
      chartEntity.mChartDataBuy[indexM] = ChartBuyUpSellData(
        tm,
        ChartBuyUpSellEntity.getYBuy(data.buyUpValueM),
      );
      chartEntity.mChartDataSell[indexM] = ChartBuyUpSellData(
        tm,
        ChartBuyUpSellEntity.getYSell(data.sellDownValueM),
      );
      update = true;
    }
    if (tl.isNotEmpty && indexL >= 0) {
      chartEntity.lChartDataBuy[indexL] = ChartBuyUpSellData(
        tl,
        ChartBuyUpSellEntity.getYBuy(data.buyUpValueL),
      );
      chartEntity.lChartDataSell[indexL] = ChartBuyUpSellData(
        tl,
        ChartBuyUpSellEntity.getYSell(data.sellDownValueL),
      );
      update = true;
    }
    if (update) {
      emit(state.copyWith(chartEntity: chartEntity));
    }
  }

  @override
  Future<void> close() {
    unsubscribeBuyUpSellDownMarket();
    return super.close();
  }
}
