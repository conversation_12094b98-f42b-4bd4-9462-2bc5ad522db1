import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_money/core/repository/money_repository.dart';
import 'package:vp_money/features/money_history/enum/money_account_type_enum.dart';
import 'package:vp_money/features/money_history/enum/money_time_filter_enum.dart'
    show MoneyTimeFilterEnum, MoneyTimeFilterEnumExt;
import 'package:vp_money/features/money_history/enum/money_transfer_type_enum.dart';
import 'package:vp_money/model/response/money_history/money_cash_transfer_hist_obj.dart';

part 'money_history_cubit.freezed.dart';
part 'money_history_state.dart';

class MoneyHistoryCubit extends Cubit<MoneyHistoryState> {
  MoneyHistoryCubit() : super(MoneyHistoryState.initial());
  final MoneyRepository _repository = GetIt.instance<MoneyRepository>();
  final listAccount = GetIt.instance<SubAccountCubit>().state.subAccountsAll;
  final derivativeAccount = GetIt.instance<SubAccountCubit>().derivativeAccount;

  Future<void> initData() async {
    final defaultSubAccount =
        GetIt.instance<SubAccountCubit>().defaultSubAccount;

    emit(
      state.copyWith(
        selectedAccount: AccountTypeEnumExt.checkSelectedAccount(
          defaultSubAccount,
        ),
      ),
    );
    fetchTransactions();
  }

  Future<void> fetchTransactions({bool isRefresh = false}) async {
    emit(state.copyWith(isLoading: true));
    try {
      final account =
          state.selectedAccount == AccountTypeEnum.derivative
              ? derivativeAccount
              : listAccount.firstWhere(
                (e) => e.accountType == state.selectedAccount.subAccountType,
              );

      final isCustom = state.selectedTime == MoneyTimeFilterEnum.custom;

      String fromDate;
      String toDate;
      if (isCustom) {
        final customDateRange = MoneyTimeFilterEnumExt.getCustomDateRange(
          state.customDateRange!,
        );
        fromDate = customDateRange['fromDate']!;
        toDate = customDateRange['toDate']!; 
      } else {
        fromDate = state.selectedTime.fromDate;
        toDate = state.selectedTime.toDate;
      }

      final result = await _repository.cashTransferHist(
        account?.id ?? "",
        fromDate,
        toDate,
        state.selectedTransferType.textQuery,
      );
      emit(state.copyWith(listTransactions: result));
    } catch (err) {
      emit(state.copyWith(errorMessage: await getErrorMessage(err)));
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  void selectedTransferType(MoneyTransferTypeEnum selectedTransferType) {
    emit(state.copyWith(selectedTransferType: selectedTransferType));
    fetchTransactions();
  }

  void changeFilter(
    AccountTypeEnum accountType,
    MoneyTimeFilterEnum timeFilter,
    DateTimeRange? dateRange,
  ) {
    emit(
      state.copyWith(
        selectedAccount: accountType,
        selectedTime: timeFilter,
        customDateRange: dateRange,
      ),
    );
    fetchTransactions();
  }

  void refresh() {
    fetchTransactions(isRefresh: true);
  }

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }
}
