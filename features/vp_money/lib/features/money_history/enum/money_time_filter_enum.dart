import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_money/generated/l10n.dart';

enum MoneyTimeFilterEnum { oneMonth, twoMonths, threeMonths, sixMonths, custom }

extension MoneyTimeFilterEnumExt on MoneyTimeFilterEnum {
  String get displayName {
    switch (this) {
      case MoneyTimeFilterEnum.oneMonth:
        return '1 ${S.current.money_month}';
      case MoneyTimeFilterEnum.twoMonths:
        return '2 ${S.current.money_month}';
      case MoneyTimeFilterEnum.threeMonths:
        return '3 ${S.current.money_month}';
      case MoneyTimeFilterEnum.sixMonths:
        return '6 ${S.current.money_month}';
      case MoneyTimeFilterEnum.custom:
        return S.current.money_custom;
    }
  }

  /// Helper method to convert any DateTime to UTC+7 timezone
  static DateTime _convertToUtcPlus7(DateTime dateTime) {
    // Convert to UTC first
    final utcDateTime = dateTime.toUtc();
    // Add 7 hours to get UTC+7
    return utcDateTime.add(const Duration(hours: 7));
  }

  /// Helper method to format timezone offset as +07:00 (always UTC+7)
  static String _formatTimezoneOffset() {
    return '+07:00';
  }

  String get fromDate {
    // Get current local time and convert to UTC+7
    final now = _convertToUtcPlus7(DateTime.now());

    // Calculate from date based on filter type, set time to 00:00:00
    final DateTime fromDateTime;
    switch (this) {
      case MoneyTimeFilterEnum.oneMonth:
        fromDateTime = DateTime(
          now.year,
          now.month - 1,
          now.day,
          0, // 00:00:00
          0,
          0,
        );
        break;
      case MoneyTimeFilterEnum.twoMonths:
        fromDateTime = DateTime(
          now.year,
          now.month - 2,
          now.day,
          0, // 00:00:00
          0,
          0,
        );
        break;
      case MoneyTimeFilterEnum.threeMonths:
        fromDateTime = DateTime(
          now.year,
          now.month - 3,
          now.day,
          0, // 00:00:00
          0,
          0,
        );
        break;
      case MoneyTimeFilterEnum.sixMonths:
        fromDateTime = DateTime(
          now.year,
          now.month - 6,
          now.day,
          0, // 00:00:00
          0,
          0,
        );
        break;
      case MoneyTimeFilterEnum.custom:
        fromDateTime = DateTime(
          now.year,
          now.month - 1,
          now.day,
          0, // 00:00:00
          0,
          0,
        );
        break;
    }

    // Format with UTC+7 timezone offset
    final offsetString = _formatTimezoneOffset();
    return '${AppTimeUtils.formatTime(fromDateTime.toString(), format: AppTimeUtilsFormat.dateTimeStandard)}$offsetString';
  }

  String get toDate {
    // Get current local time and convert to UTC+7
    final now = _convertToUtcPlus7(DateTime.now());

    // Format with UTC+7 timezone offset
    final offsetString = _formatTimezoneOffset();
    return '${AppTimeUtils.formatTime(now.toString(), format: AppTimeUtilsFormat.dateTimeStandard)}$offsetString';
  }

  static Map<String, String> getCustomDateRange(DateTimeRange dateRange) {
    // Get current time and convert to UTC+7
    final now = _convertToUtcPlus7(DateTime.now());

    // Start date: set time to 00:00:00
    final startDateTime = DateTime(
      dateRange.start.year,
      dateRange.start.month,
      dateRange.start.day,
      0, // 00:00:00
      0,
      0,
    );

    // End date: use current time converted to UTC+7
    final endDateTime = DateTime(
      dateRange.end.year,
      dateRange.end.month,
      dateRange.end.day,
      now.hour,
      now.minute,
      now.second,
    );

    // Format with UTC+7 timezone offset
    final startOffsetString = _formatTimezoneOffset();
    final endOffsetString = _formatTimezoneOffset();

    final fromDate =
        '${AppTimeUtils.formatTime(startDateTime.toString(), format: AppTimeUtilsFormat.dateTimeStandard)}$startOffsetString';
    final toDate =
        '${AppTimeUtils.formatTime(endDateTime.toString(), format: AppTimeUtilsFormat.dateTimeStandard)}$endOffsetString';

    return {'fromDate': fromDate, 'toDate': toDate};
  }
}
