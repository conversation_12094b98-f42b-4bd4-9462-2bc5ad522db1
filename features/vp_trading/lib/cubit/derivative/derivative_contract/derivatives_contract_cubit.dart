import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/core/derivative/base_response/base_response.dart';
import 'package:vp_trading/core/repository/derivative_repository.dart';
import 'package:vp_trading/generated/assets.gen.dart';

part 'derivatives_contract_state.dart';

class DerivativesContractCubit extends Cubit<DerivativesContractState> {
  DerivativesContractCubit() : super(DerivativesContractState());

  final DerivativeRepository _repository =
      GetIt.instance<DerivativeRepository>();

  Future<BaseResponseMeta?> openOrReactiveAccountDerivatives(
    VoidCallback onResult,
    VoidCallback onFail,
  ) async {
    try {
      var derivativeAccount =
          GetIt.instance.get<SubAccountCubit>().derivativeAccount;
      bool isReactiveDerivative =
          derivativeAccount?.isReactiveDerivative ?? false;
      emit(state.copyWith(isLoading: true));
      final result =
          await (isReactiveDerivative
              ? _repository.reactiveAccountDerivative()
              : _repository.createAccountDerivative());
      if (result.isSuccess()) {
        emit(state.copyWith(isLoading: false));
        eventBus.fire(GetSubAccountEvent());
        onResult();
        return result.data;
      } else {
        emit(state.copyWith(isLoading: false));

        /// Tạm comment task AC-476
        switch (result.code) {
          case needUpdateCCCD12:
          case customerWaitApproval:
            showErrorMessage(result.message);
            break;
          default:
            if (result.status != closeInputOTPErrorCode) {
              showErrorMessage(result.message);
            }
        }
        onFail();
      }
    } catch (e) {
      emit(state.copyWith(isLoading: false));
      showError(e);
      onFail();
    }
    return null;
  }

  Future<Uint8List?> getContractData() async {
    try {
      if (DownloadUtils().checkUrlValid(AppConstants.derivativeContract)) {
        Uint8List? data = await DownloadUtils().getUrlContent(
          AppConstants.derivativeContract,
        );
        if (data != null) {
          return data;
        }
      }
      return Future.error('');
    } catch (e) {
      return Future.error('');
    }
  }

  void downloadContract(
    BuildContext context,
    String fileName,
    String url,
  ) async {
    final randomId = AppHelper().genXRequestID();
    final String tag = 'download_contract_$randomId';
    EasyDebounce.debounce(tag, const Duration(milliseconds: 1000), () async {
      try {
        showSnackBar(
          context,
          VPCommonLocalize.current.downloading,
          asset: VpTradingAssets.icons.icDownload.path,
          package: VpTradingAssets.package,
          colorAsset: themeData.black,
        );
        final String defaultFileName = fileName;
        final downloadResult = await DownloadFileManager.instance.downloadFile(
          fileName: defaultFileName,
          extension: FileExtension.pdf,
          url: url,
        );

        showSnackBar(
          context,
          downloadResult != null
              ? VPCommonLocalize.current.download_success
              : VPCommonLocalize.current.download_fail,
          asset:
              downloadResult != null
                  ? Assets.icons.icSuccess.path
                  : Assets.icons.icFail.path,
        );
      } catch (e) {
        showSnackBar(
          context,
          VPCommonLocalize.current.download_fail,
          asset: Assets.icons.icFail.path,
        );
      }
    });
  }
}
