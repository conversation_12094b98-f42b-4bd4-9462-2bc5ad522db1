import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:vp_assets/core/repository/asset_repository.dart';
import 'package:vp_assets/model/asset_overview/assset_summary_model.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/cubit/derivative/mixin/derivative_account_mixin.dart';

part 'derivatives_market_state.dart';

class DerivativesMarketCubit extends Cubit<DerivativesMarketState>
    with DerivativeAccountMixin {
  DerivativesMarketCubit() : super(const DerivativesMarketState());

  final AssetRepository _assetRepository = GetIt.instance.get();

  late SubAccountModel? derivativeAccount =
      GetIt.instance.get<SubAccountCubit>().derivativeActiveAccount;

  Future<void> loadData() async {
    try {
      emit(state.copyWith(isLoading: true));

      derivativeAccount = await mediator.send(
        GetSubAccountQuery<SubAccountModel?>(),
      );

      if (derivativeAccount == null || !isShowDerivativeAssets) {
        emit(state.copyWith(isLoading: false));

        return;
      }

      var fuAssetSummary = await _assetRepository.getSummaryAccounts(
        derivativeAccount!.id,
      );

      emit(state.copyWith(isLoading: false, fuAssetSummary: fuAssetSummary));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(isLoading: false));
      showError(e);
    }
  }
}
