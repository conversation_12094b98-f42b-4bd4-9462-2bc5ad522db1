part of 'derivatives_market_cubit.dart';

final class DerivativesMarketState extends Equatable {
  final bool isLoading;
  final AsssetSummaryModel? fuAssetSummary;

  const DerivativesMarketState({this.isLoading = false, this.fuAssetSummary});

  DerivativesMarketState copyWith({
    bool? isLoading,
    AsssetSummaryModel? fuAssetSummary,
  }) {
    return DerivativesMarketState(
      isLoading: isLoading ?? this.isLoading,
      fuAssetSummary: fuAssetSummary ?? this.fuAssetSummary,
    );
  }

  @override
  List<Object?> get props => [isLoading, fuAssetSummary];
}
