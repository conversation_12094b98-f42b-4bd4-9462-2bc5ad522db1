import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vp_stock_common/model/place_order_args.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/screen/derivative_home_page.dart';
import 'package:vp_trading/screen/derivative_sign_up/derivatives_contract_page.dart';
import 'package:vp_trading/screen/derivative_sign_up/sign_up_success_page.dart';
import 'package:vp_trading/screen/market/derivative_market_page.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/derivatives_order_book_screen.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_portfolio/derivatives_portfolio_screen.dart';
import 'package:vp_trading/screen/place_order/derivative/trading_derivative_page.dart';
import 'package:vp_trading/screen/place_order/derivative/widget/derivative_setting_confirm_transaction_switch/derivative_setting_confirm_transaction_switch.dart';
import 'package:vp_trading/screen/place_order/main/trading_order_main_screen.dart';

enum TradingRouter {
  placeOrder('/placeOrder'),
  derivativesPortfolio('/derivatives_portfolio'),
  derivativesOrderBookScreen('/derivativesOrderBookScreen'),
  derivativeSettingConfirmTransactionSwitch(
    '/DerivativeSettingConfirmTransactionSwitch',
  ),
  derivativeMarketPage('/derivativeMarketPage'),
  derivativeSignUpSuccess('/derivativeSignUpSuccess'),
  derivativeContract('/derivativeContract'),
  moneyTransfer('/moneyTranfer'),
  moneyCashIn('/moneyCashIn'),
  derivativeHomePage('/derivativeHomePage'),
  tradingDerivativePage('/tradingDerivativePage'),
  derivativeTransaction('/derivativeTransaction');

  final String routeName;

  const TradingRouter(this.routeName);
}

List<RouteBase> tradingRouter() {
  return [
    GoRoute(
      name: TradingRouter.placeOrder.routeName,
      path: TradingRouter.placeOrder.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          fullscreenDialog: true,
          child: TradingOrderMainScreen(
            args: PlaceOrderArgsExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          ),
        );
      },
    ),
    GoRoute(
      name: TradingRouter.derivativesPortfolio.routeName,
      path: TradingRouter.derivativesPortfolio.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          fullscreenDialog: true,
          child: const DerivativesPortfolioScreen(),
        );
      },
    ),
    GoRoute(
      name: TradingRouter.derivativesOrderBookScreen.routeName,
      path: TradingRouter.derivativesOrderBookScreen.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          fullscreenDialog: true,
          child: const DerivativesOrderBookScreen(),
        );
      },
    ),
    GoRoute(
      name: TradingRouter.derivativeMarketPage.routeName,
      path: TradingRouter.derivativeMarketPage.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          fullscreenDialog: true,
          child: const DerivativeMarketPage(),
        );
      },
    ),
    GoRoute(
      path: TradingRouter.derivativeSettingConfirmTransactionSwitch.routeName,
      pageBuilder:
          (context, state) => NoTransitionPage<void>(
            key: state.pageKey,
            child: DerivativeSettingConfirmTransactionSwitch(
              status: state.extra as bool?,
            ),
          ),
    ),
    GoRoute(
      name: TradingRouter.derivativeHomePage.routeName,
      path: TradingRouter.derivativeHomePage.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          fullscreenDialog: true,
          child: const DerivativeHomePage(),
        );
      },
    ),
    GoRoute(
      name: TradingRouter.tradingDerivativePage.routeName,
      path: TradingRouter.tradingDerivativePage.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          fullscreenDialog: true,
          child: TradingDerivativePage(
            args: PlaceOrderArgs(orderType: PlaceOrderType.derivative),
          ),
        );
      },
    ),
    GoRoute(
      name: TradingRouter.derivativeSignUpSuccess.routeName,
      path: TradingRouter.derivativeSignUpSuccess.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          child: const SignUpSuccessPage(),
        );
      },
    ),
    GoRoute(
      name: TradingRouter.derivativeContract.routeName,
      path: TradingRouter.derivativeContract.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          child: const DerivativesContractPage(),
        );
      },
    ),
  ];
}
