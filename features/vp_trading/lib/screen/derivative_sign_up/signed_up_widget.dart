import 'package:flutter/material.dart';
import 'package:vp_assets/model/asset_overview/assset_summary_model.dart';
import 'package:vp_assets/vp_assets.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/derivative_sign_up/widgets/bottom_sheet_tooltip_widget.dart';
import 'package:vp_trading/screen/derivative_sign_up/widgets/deposit_statistics_widget.dart';
import 'package:vp_trading/screen/derivative_sign_up/widgets/deposit_widget.dart';
import 'package:vp_trading/screen/derivative_sign_up/widgets/row_title_widget.dart';

class ArgumentAssetOverviewPage {
  final SubAccountType? subAccountType;

  final int initialPage;

  const ArgumentAssetOverviewPage({this.subAccountType, this.initialPage = 0});
}

class SignedUpWidget extends StatelessWidget {
  final AsssetSummaryModel? model;

  const SignedUpWidget({super.key, this.model});

  @override
  Widget build(BuildContext context) {
    var derivativeAccount =
        GetIt.instance<AuthCubit>().userInfo?.userinfo?.custodycd;
    return DecoratedBox(
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation1,
        borderRadius: BorderRadius.circular(2),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  "$derivativeAccount-8",
                  style: vpTextStyle.subtitle16.copyColor(themeData.text),
                ),
                const Spacer(),
                InkWell(
                  child: Text(
                    VPTradingLocalize.current.derivative_see_detail,
                    style: vpTextStyle.subtitle16.copyColor(themeData.primary),
                  ),
                  onTap:
                      () => _navigateToAsset(
                        context: context,
                        subAccountType: SubAccountType.derivative,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            RowTitleWidget(
              title: VPTradingLocalize.current.derivative_net_assets,
              value: model?.totalNav.toMoney(symbol: "đ") ?? "--",
            ),
            RowTitleWidget(
              title:
                  VPTradingLocalize.current.derivative_profit_loss_during_day,
              value: model?.pnl.toMoney(symbol: "đ") ?? "--",
              colorValue: StockColorUtils.colorByValue(price: model?.pnl),
              isToolTip: true,
              onTapToolTip:
                  () => _onShowTooltip(
                    context: context,
                    bottomSheetContent:
                        VPTradingLocalize.current.derivative_vmamt_content,
                  ),
            ),
            RowTitleWidget(
              title: VPTradingLocalize.current.derivative_tkps_usage_rate,
              value:
                  model?.margin?.derAccountRatio.toPercent(
                    addPrefixCharacter: false,
                    truncateZero: true,
                  ) ??
                  '--',
              colorValue: model?.margin?.color,
            ),
            const SizedBox(height: 32),
            DepositStatistics(model: model),
            const SizedBox(height: 16),
            DepositWidget(model: model),
          ],
        ),
      ),
    );
  }

  void _onShowTooltip({
    required BuildContext context,
    String? bottomSheetContent,
  }) {
    showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return AppBottomSheet(
          child: TooltipBottomSheet(bottomSheetContent: bottomSheetContent),
        );
      },
    );
  }

  void _navigateToAsset({
    required BuildContext context,
    int initialPage = 0,
    SubAccountType? subAccountType,
  }) {
    context.push(
      AssetRouter.assetOverview.routeName,
      extra: AssetOverviewPageArgument(
        initialPage: initialPage,
        subAccountType: subAccountType,
      ),
    );
  }
}
