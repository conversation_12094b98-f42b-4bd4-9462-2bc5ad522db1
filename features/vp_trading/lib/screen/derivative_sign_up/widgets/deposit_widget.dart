import 'package:flutter/material.dart';
import 'package:vp_assets/model/asset_overview/assset_summary_model.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/router/trading_router.dart';
import 'package:vp_trading/screen/derivative_sign_up/widgets/bottom_sheet_tooltip_widget.dart';
import 'package:vp_trading/screen/place_order/derivative/widget/button_widget.dart';

class DepositWidget extends StatelessWidget {
  final AsssetSummaryModel? model;

  const DepositWidget({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  VPTradingLocalize.current.derivative_amount_be_added,
                  style: vpTextStyle.captionMedium.copyColor(themeData.gray500),
                ),
                const SizedBox(width: 4),
                InkWell(
                  onTap:
                      () => _onShowTooltip(
                        context: context,
                        bottomSheetContent:
                            VPTradingLocalize
                                .current
                                .derivative_cash_value_needs_to_be_added,
                      ),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 2.0),
                    child: VpTradingAssets.icons.icToolTip.svg(),
                  ),
                ),
              ],
            ),
            Text(
              model?.margin?.derReqAddSecured?.toDouble().toMoney(
                    symbol: "đ",
                  ) ??
                  '--',
              style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
            ),
          ],
        ),
        const Spacer(),
        ButtonWidget(
          action: 'Nạp tiền',
          colorBorder: themeData.primary,
          textStyle: vpTextStyle.subtitle14.copyColor(themeData.primary),
          colorEnable: vpColor.backgroundElevation1,
          onPressed: () => context.push(TradingRouter.moneyCashIn.routeName),
        ),
      ],
    );
  }

  void _onShowTooltip({
    required BuildContext context,
    String? bottomSheetContent,
  }) {
    showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return AppBottomSheet(
          child: TooltipBottomSheet(bottomSheetContent: bottomSheetContent),
        );
      },
    );
  }
}
