import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/derivative_utility/drivative_utils.dart';
import 'package:vp_trading/vp_trading_navigator.dart';
import 'package:vp_trading/widgets/service_item_widget.dart';

typedef MenuItem = ({VoidCallback onTap, Widget icon, String title});

class DerivativeUtilityPage extends StatelessWidget {
  const DerivativeUtilityPage({super.key});

  SubAccountModel? get subAccount =>
      GetIt.instance.get<SubAccountCubit>().derivativeActiveAccount;

  void onOpenDerivativeTransaction(BuildContext context) {
    final isValid = DerivativeUtils.checkDerivativeAccount(context);

    if (!isValid) return;

    const routeToScreenList = ["A", "W", "S", "B"];

    if (routeToScreenList.contains(subAccount?.dmaStatus)) {
      tradingNavigator.openDerivativeTransaction(
        context,
        args: DerivativeTransactionArgs(
          account:
              GetIt.instance.get<SubAccountCubit>().derivativeActiveAccount!,
          custId: GetIt.instance.get<SubAccountCubit>().state.custId!,
          custodyCd: GetIt.instance.get<SubAccountCubit>().state.custodyCd!,
        ),
      );
    }
  }

  void openDerivativeIncomeStatement(BuildContext context) {
    final isValid = DerivativeUtils.checkDerivativeAccount(context);

    if (isValid) {
      context.pushNamed(
        '/derivativeIncomeStatement',
        queryParameters: {'accountId': subAccount!.id},
      );
    }
  }

  void openDerivativeTransactionHistory(BuildContext context) {
    final isValid = DerivativeUtils.checkDerivativeAccount(context);

    if (isValid) {
      context.pushNamed(
        '/derivativeTransactionHistory',
        queryParameters: subAccount!.toQueryParams(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final menuItems = <MenuItem>[
      (
        onTap: () => onOpenDerivativeTransaction(context),
        icon: VpTradingAssets.icons.icTransactionMoney.svg(
          width: 24,
          height: 24,
        ),
        title: VPTradingLocalize.current.derivativeDepositOrWithdrawDerivative,
      ),
      (
        onTap: () => openDerivativeIncomeStatement(context),
        icon: VpTradingAssets.icons.icIncomeStatement.svg(
          width: 24,
          height: 24,
        ),
        title: VPTradingLocalize.current.derivativeIncomeStatement,
      ),
      (
        onTap: () => openDerivativeTransactionHistory(context),
        icon: VpTradingAssets.icons.icTransactionHistory.svg(
          width: 24,
          height: 24,
        ),
        title: VPTradingLocalize.current.derivativeTransactionHistory,
      ),
    ];

    return VPScaffold(
      backgroundColor: vpColor.backgroundElevationMinus1,
      appBar: VPAppBar.layer(
        title: 'Tiện ích',
        subTitle: 'Phái sinh',
        revertSubTitle: true,
      ),
      body: GridView.count(
        shrinkWrap: true,
        crossAxisCount: 2,
        childAspectRatio: 2.0,
        mainAxisSpacing: 16.0,
        crossAxisSpacing: 16.0,
        padding: const EdgeInsets.all(16.0),
        physics: const NeverScrollableScrollPhysics(),
        children:
            menuItems.map((item) {
              return ServiceItemWidget(
                onTap: item.onTap,
                icon: item.icon,
                title: item.title,
              );
            }).toList(),
      ),
    );
  }
}
