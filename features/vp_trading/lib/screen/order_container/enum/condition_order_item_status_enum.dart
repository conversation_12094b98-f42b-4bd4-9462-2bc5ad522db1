import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';

enum ConditionOrderViewStatusEnum {
  activated,
  waitingToSend,
  waiting,
  waitingForActivation,
  sent,
  partialMatch,
  fullMatch,
  edited,
  canceled,
  rejected,
  expired,
  ineffective,
}

extension ConditionOrderViewStatusEnumExt on ConditionOrderViewStatusEnum {
  String get title {
    switch (this) {
      case ConditionOrderViewStatusEnum.activated:
        return "Đã kích hoạt";
      case ConditionOrderViewStatusEnum.waitingToSend:
        return "Chờ gửi";
      case ConditionOrderViewStatusEnum.waiting:
        return "Đang chờ";
      case ConditionOrderViewStatusEnum.waitingForActivation:
        return "Chờ kích hoạt";
      case ConditionOrderViewStatusEnum.sent:
        return "Đã gửi";
      case ConditionOrderViewStatusEnum.partialMatch:
        return "Khớp 1 phần";
      case ConditionOrderViewStatusEnum.fullMatch:
        return "Khớp hết";
      case ConditionOrderViewStatusEnum.edited:
        return "Đã sửa";
      case ConditionOrderViewStatusEnum.canceled:
        return "Đã huỷ";
      case ConditionOrderViewStatusEnum.rejected:
        return "Từ chối";
      case ConditionOrderViewStatusEnum.expired:
        return "Hết hạn";
      case ConditionOrderViewStatusEnum.ineffective:
        return "Hết hiệu lực";
    }
  }

  Color get color {
    switch (this) {
      case ConditionOrderViewStatusEnum.activated:
        return vpColor.backgroundAccentGreen;
      case ConditionOrderViewStatusEnum.waitingToSend:
      case ConditionOrderViewStatusEnum.waitingForActivation:
      case ConditionOrderViewStatusEnum.waiting:
      case ConditionOrderViewStatusEnum.sent:
      case ConditionOrderViewStatusEnum.partialMatch:
        return vpColor.backgroundAccentBlue;
      case ConditionOrderViewStatusEnum.fullMatch:
        return vpColor.backgroundAccentGreen;
      case ConditionOrderViewStatusEnum.edited:
      case ConditionOrderViewStatusEnum.canceled:
      case ConditionOrderViewStatusEnum.rejected:
      case ConditionOrderViewStatusEnum.expired:
      case ConditionOrderViewStatusEnum.ineffective:
        return vpColor.backgroundAccentRed;
    }
  }

  Color get textColor {
    switch (this) {
      case ConditionOrderViewStatusEnum.activated:
        return vpColor.textAccentGreen;
      case ConditionOrderViewStatusEnum.waitingToSend:
      case ConditionOrderViewStatusEnum.waitingForActivation:
      case ConditionOrderViewStatusEnum.waiting:
      case ConditionOrderViewStatusEnum.sent:
      case ConditionOrderViewStatusEnum.partialMatch:
        return vpColor.textAccentBlue;
      case ConditionOrderViewStatusEnum.fullMatch:
        return vpColor.textAccentGreen;
      case ConditionOrderViewStatusEnum.edited:
      case ConditionOrderViewStatusEnum.canceled:
      case ConditionOrderViewStatusEnum.rejected:
      case ConditionOrderViewStatusEnum.expired:
      case ConditionOrderViewStatusEnum.ineffective:
        return vpColor.textAccentRed;
    }
  }

  static ConditionOrderViewStatusEnum conditionOrderStatusFromString(
    String? code,
  ) {
    switch (code?.toUpperCase()) {
      case "ACTIVATED":
        return ConditionOrderViewStatusEnum.activated;
      case "PS":
        return ConditionOrderViewStatusEnum.waitingToSend;
      case "INACTIVATED":
        return ConditionOrderViewStatusEnum.waitingForActivation;
      case "WT":
      case "WA":
      case "WC":
      case "WE":
      case "PR":
      case "PC":
      case "OP":
      case "WD":
        return ConditionOrderViewStatusEnum.waiting;
      case "ST":
        return ConditionOrderViewStatusEnum.sent;
      case "PF":
        return ConditionOrderViewStatusEnum.partialMatch;
      case "FF":
      case "CP":
        return ConditionOrderViewStatusEnum.fullMatch;
      case "RP":
        return ConditionOrderViewStatusEnum.edited;
      case "CANCELED":
      case "CN":
        return ConditionOrderViewStatusEnum.canceled;
      case "RJ":
        return ConditionOrderViewStatusEnum.rejected;
      case "EX":
        return ConditionOrderViewStatusEnum.expired;
      case "EP":
      case "EXPIRED":
        return ConditionOrderViewStatusEnum.ineffective;
      default:
        return ConditionOrderViewStatusEnum.waitingForActivation;
    }
  }
}
