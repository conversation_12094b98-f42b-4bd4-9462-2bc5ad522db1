import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';

enum ConditionOrderStatusEnum {
  all,
  inactivated,
  activated,
  canceled,
  partialMatch,
  fullMatch,
}

extension ConditionOrderStatusEnumExt on ConditionOrderStatusEnum {
  String get title {
    switch (this) {
      case ConditionOrderStatusEnum.all:
        return "Tất cả";
      case ConditionOrderStatusEnum.inactivated:
        return "Lệnh chờ";
      case ConditionOrderStatusEnum.activated:
        return "Đã kích hoạt";
      case ConditionOrderStatusEnum.canceled:
        return "Lệnh huỷ";
      case ConditionOrderStatusEnum.partialMatch:
        return "Khớp 1 phần";
      case ConditionOrderStatusEnum.fullMatch:
        return "Khớp hết";
    }
  }

  Color get color {
    switch (this) {
      case ConditionOrderStatusEnum.all:
        return Colors.black54;
      case ConditionOrderStatusEnum.inactivated:
        return vpColor.backgroundAccentYellow;
      case ConditionOrderStatusEnum.activated:
        return vpColor.backgroundAccentGreen;
      case ConditionOrderStatusEnum.canceled:
        return vpColor.backgroundAccentRed;
      case ConditionOrderStatusEnum.partialMatch:
        return vpColor.backgroundAccentBlue;
      case ConditionOrderStatusEnum.fullMatch:
        return vpColor.backgroundAccentGreen;
    }
  }

  Color get textColor {
    switch (this) {
      case ConditionOrderStatusEnum.all:
        return Colors.black54;
      case ConditionOrderStatusEnum.inactivated:
        return vpColor.textAccentYellow;
      case ConditionOrderStatusEnum.activated:
        return vpColor.textAccentGreen;
      case ConditionOrderStatusEnum.canceled:
        return vpColor.textAccentRed;
      case ConditionOrderStatusEnum.partialMatch:
        return vpColor.textAccentBlue;
      case ConditionOrderStatusEnum.fullMatch:
        return vpColor.textAccentGreen;
    }
  }

  String get codeRequest {
    switch (this) {
      case ConditionOrderStatusEnum.all:
        return "ALL";
      case ConditionOrderStatusEnum.inactivated:
        return "INACTIVATED,PS,WT,WA,WC,WE,PR,PC,OP,WD,ST";
      case ConditionOrderStatusEnum.activated:
        return "ACTIVATED";
      case ConditionOrderStatusEnum.canceled:
        return "CANCELED,CN,RP,RJ,EX,EP,EXPIRED";
      case ConditionOrderStatusEnum.partialMatch:
        return "PF";
      case ConditionOrderStatusEnum.fullMatch:
        return "FF,CP";
    }
  }

  static ConditionOrderStatusEnum conditionOrderStatusFromString(String? code) {
    switch (code?.toUpperCase()) {
      case "INACTIVATED":
      case "PS":
      case "WT":
      case "WA":
      case "WC":
      case "WE":
      case "PR":
      case "PC":
      case "OP":
      case "WD":
      case "ST":
        return ConditionOrderStatusEnum.inactivated;
      case "ACTIVATED":
        return ConditionOrderStatusEnum.activated;
      case "CN":
      case "RP":
      case "CANCELED":
      case "RJ":
      case "EX":
      case "EP":
      case "EXPIRED":
        return ConditionOrderStatusEnum.canceled;
      case "PF":
        return ConditionOrderStatusEnum.partialMatch;
      case "FF":
      case "CP":
        return ConditionOrderStatusEnum.fullMatch;
      default:
        return ConditionOrderStatusEnum.inactivated; // fallback
    }
  }

  static String displayNameFilter(List<ConditionOrderStatusEnum> listStatus) {
    if (listStatus.contains(ConditionOrderStatusEnum.all)) {
      return VPTradingLocalize.current.trading_status_all;
    } else {
      if (listStatus.length == 1) {
        return listStatus.first.title;
      }
      return "Đã chọn ${listStatus.length}";
    }
  }
}
