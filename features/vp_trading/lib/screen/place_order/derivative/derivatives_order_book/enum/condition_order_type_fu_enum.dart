enum ConditionOrderTypeFuEnum { all, bb, tso, sto, tpsl }

extension ConditionOrderTypeFuEnumExt on ConditionOrderTypeFuEnum {
  String get title {
    switch (this) {
      case ConditionOrderTypeFuEnum.all:
        return "Tất cả";
      case ConditionOrderTypeFuEnum.bb:
        return "StopLoss/Take Profit";
      case ConditionOrderTypeFuEnum.tso:
        return "Trailing Stop";
      case ConditionOrderTypeFuEnum.sto:
        return "Stop Order";
      case ConditionOrderTypeFuEnum.tpsl:
        return "SL/TP(Danh mục)";
    }
  }

  String? get titleEdit {
    switch (this) {
      case ConditionOrderTypeFuEnum.bb:
        return "Sửa lệnh StopLoss/Take Profit";
      case ConditionOrderTypeFuEnum.tso:
        return "Sửa lệnh Trailling Stop";
      case ConditionOrderTypeFuEnum.sto:
        return "Sửa lệnh Stop Order";
      case ConditionOrderTypeFuEnum.tpsl:
        return "Sửa lệnh SL/TP(<PERSON>h mục)";
      default:
        return "";
    }
  }

  String get codeRequest {
    switch (this) {
      case ConditionOrderTypeFuEnum.all:
        return "ALL";
      case ConditionOrderTypeFuEnum.bb:
        return "BB";
      case ConditionOrderTypeFuEnum.tso:
        return "TSO";
      case ConditionOrderTypeFuEnum.sto:
        return "STO";
      case ConditionOrderTypeFuEnum.tpsl:
        return "TPSL";
    }
  }

  static ConditionOrderTypeFuEnum conditionOrderTypeFuFromString(String? code) {
    switch (code?.toUpperCase()) {
      case "BB":
        return ConditionOrderTypeFuEnum.bb;
      case "TSO":
        return ConditionOrderTypeFuEnum.tso;
      case "STO":
        return ConditionOrderTypeFuEnum.sto;
      case "TPSL":
        return ConditionOrderTypeFuEnum.tpsl;
      default:
        return ConditionOrderTypeFuEnum.bb; // fallback
    }
  }
}