import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/screen/place_order/derivative/trading_derivative_view.dart';
import 'package:vp_trading/screen/place_order/place_order_page.dart';
import 'package:vp_trading/screen/place_order/widgets/appbar/place_order_app_bar_view.dart';

class TradingOrderMainScreen extends StatefulWidget {
  const TradingOrderMainScreen({super.key, required this.args});

  final PlaceOrderArgs args;

  @override
  State<TradingOrderMainScreen> createState() => _TradingOrderMainScreenState();
}

class _TradingOrderMainScreenState extends State<TradingOrderMainScreen>
    with TickerProviderStateMixin {
  final _pageController = PageController();

  late final TabController _selectorController = TabController(
    length: _pages.length,
    vsync: this,
  );

  PlaceOrderArgs? getArgs(PlaceOrderType type) {
    return widget.args.orderType == type ? widget.args : null;
  }

  late final List<Widget> _pages = [
    PlaceOrderPage(args: getArgs(PlaceOrderType.normal)),
    TradingDerivativeView(args: getArgs(PlaceOrderType.derivative)),
  ];

  Duration get _duration => const Duration(milliseconds: 200);

  PlaceOrderType orderType = PlaceOrderType.normal;

  @override
  void initState() {
   
    super.initState();
    eventBus.fire(GetSubAccountEvent());
  }

  @override
  void dispose() {
    _pageController.dispose();
    _selectorController.dispose();
    super.dispose();
  }

  void _changePage(PlaceOrderType type) {
    if (!_pageController.hasClients) return;

    if (type == PlaceOrderType.normal) {
      _pageController.jumpToPage(0);
    } else {
      _pageController.animateToPage(1, duration: _duration, curve: Curves.ease);
    }

    setState(() => orderType = type);
  }

  @override
  Widget build(BuildContext context) {
    return VPScaffold(
      backgroundColor: vpColor.backgroundElevationMinus1,
      extendBody: true,
      body: Column(
        children: [
          PlaceOrderAppBarView(
            current: orderType,
            onTab: (p0) => _changePage(p0),
          ),
          Expanded(
            child: PageView.builder(
              physics: const NeverScrollableScrollPhysics(),
              controller: _pageController,
              itemCount: _pages.length,
              onPageChanged:
                  (page) => _changePage(
                    page == 0
                        ? PlaceOrderType.normal
                        : PlaceOrderType.derivative,
                  ),
              itemBuilder: (_, index) => _pages[index],
            ),
          ),
        ],
      ),
    );
  }
}
