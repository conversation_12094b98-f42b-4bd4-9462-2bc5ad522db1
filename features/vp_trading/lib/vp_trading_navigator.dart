import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/router/trading_router.dart';

import 'generated/assets.gen.dart';

TradingNavigator get tradingNavigator => GetIt.instance.get<TradingNavigator>();

abstract class TradingNavigator {
  Future openFuSymbolSelectorBottomSheet(
    BuildContext context, {
    required List<FuQuoteModel> quotes,
  });

  void showWarningDontHaveDerivativeAccount(
    BuildContext context, {
    VoidCallback? onRegister,
  });

  void showDerivativeAccountPendingApprovalDialog(BuildContext context);

  void showInvalidAccountStatusDialog(BuildContext context);

  Future openDerivativeRegisterPage(BuildContext context);

  void openDerivativeTransaction(
    BuildContext context, {
    required DerivativeTransactionArgs args,
  });
}

class TradingNavigatorImpl extends TradingNavigator {
  @override
  Future openDerivativeRegisterPage(BuildContext context) {
    return context.push(TradingRouter.derivativeContract.routeName);
  }

  @override
  Future openFuSymbolSelectorBottomSheet(
    BuildContext context, {
    required List<FuQuoteModel> quotes,
  }) {
    return VPPopup.bottomAction(
          items:
              quotes
                  .map(
                    (e) => ListTile(
                      onTap: () => Navigator.pop(context, e),
                      title: Text(
                        e.symbol,
                        textAlign: TextAlign.center,
                        style: vpTextStyle.subtitle14.copyColor(
                          vpColor.textPrimary,
                        ),
                      ),
                    ),
                  )
                  .toList(),
        )
        .copyWith(
          button: const VpBottomSheetCancelButtonView(),
          contentPadding: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(8),
          icon: const SizedBox.shrink(),
        )
        .showAction(context);
  }

  @override
  void showWarningDontHaveDerivativeAccount(
    BuildContext context, {
    VoidCallback? onRegister,
  }) {
    VPPopup.oneButton(
          title: VPTradingLocalize.current.derivativeNotification,
          content:
              VPTradingLocalize.current.derivativeContentNotYetAccountPopup,
          image: VpTradingAssets.icons.icCancelMultiOrder.svg(),
        )
        .copyWith(
          button: VpsButton.primarySmall(
            onPressed: onRegister,
            title: VPTradingLocalize.current.derivativeDialogRegisterButton,
          ),
        )
        .showDialog(context, barrierDismissible: true);
  }

  @override
  void showDerivativeAccountPendingApprovalDialog(BuildContext context) {
    VPPopup.oneButton(
          title: VPTradingLocalize.current.derivativeNotification,
          content: VPTradingLocalize.current.derivativeAccountPendingApprove,
          image: VpTradingAssets.icons.icDerivativeAccountPendingApprove.svg(),
        )
        .copyWith(
          button: VpsButton.secondarySmall(
            title: VPTradingLocalize.current.derivativeDialogCloseButton,

            onPressed: () => context.pop(),
          ),
        )
        .showDialog(context, barrierDismissible: true);
  }

  @override
  void showInvalidAccountStatusDialog(BuildContext context) {
    VPPopup.oneButton(
          title: VPTradingLocalize.current.derivativeNotification,
          content: VPTradingLocalize.current.derivativeAccountPendingApprove,
          image: VpTradingAssets.icons.icDerivativeInvalidAccount.svg(),
        )
        .copyWith(
          button: VpsButton.secondarySmall(
            title: VPTradingLocalize.current.derivativeDialogCloseButton,
            onPressed: () => context.pop(),
          ),
        )
        .showDialog(context, barrierDismissible: true);
  }

  @override
  void openDerivativeTransaction(
    BuildContext context, {
    required DerivativeTransactionArgs args,
  }) {
    context.pushNamed(
      TradingRouter.derivativeTransaction.routeName,
      queryParameters: args.toQueryParams(),
    );
  }
}
