import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/model/positions/contract_quote_model.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/router/vp_utility_router.dart';
import 'package:vp_utility/screen/derivative_transaction/bloc/transaction_state.dart';
import 'package:vp_utility/screen/derivative_transaction/data/models/data_check_deposit_or_withdraw.dart';
import 'package:vp_utility/screen/derivative_transaction/data/repository/transaction_repository_impl.dart';

import '../data/models/sys_config_model.dart';
import '../data/system_config_constants.dart';
import '../transaction_utils/transaction_utils.dart';
import '../widget/derivative_note_dialog.dart';

class TransactionBloc extends Cubit<TransactionSate> {
  TransactionBloc({
    required this.account,
    required this.custodyCd,
    required this.custId,
  }) : super(TransactionSate());

  SubAccountModel account;

  final String custodyCd;

  final String custId;

  final DerivativeTransactionRepository _repository =
      GetIt.instance.get<DerivativeTransactionRepository>();

  final TextEditingController textContentTransferController =
      TextEditingController(text: 'Nop tien ky quy phai sinh');

  final TextEditingController textContentWithdrawController =
      TextEditingController(text: 'Rut tien ky quy phai sinh');

  final TextEditingController moneyController = TextEditingController();
  final TextEditingController someContracts = TextEditingController();

  num feeDepositAndWithdraw = 0;

  Timer? _debounce;

  bool? isSmartOTPUnRegistered;

  bool? notInputSmartOTP;

  FocusNode focusNode = FocusNode();
  bool isFocused = false;

  void onFocusChange() {
    isFocused = focusNode.hasFocus;
  }

  // void checkValidateTime({required DateTime dateTime}){
  //   // Lấy thời gian hiện tại
  //   // final now = DateTime.now();
  //
  //   // Định dạng thời gian
  //   // final DateFormat formatter = DateFormat('HH:mm');
  //   // final String formatted = formatter.format(now);
  //
  //   // Tạo thời gian bắt đầu và kết thúc
  //   final startTime = TimeOfDay(hour: 8, minute: 30);
  //   final endTime = TimeOfDay(hour: 16, minute: 0);
  //
  //   // Chuyển đổi thời gian hiện tại thành TimeOfDay
  //   final currentTime = TimeOfDay(hour: dateTime.hour, minute: dateTime.minute);
  //
  //   // Kiểm tra nếu thời gian hiện tại nằm trong khoảng từ 8h30 đến 16h
  //   final bool isWithinTimeFrame = (currentTime.hour > startTime.hour || (currentTime.hour == startTime.hour && currentTime.minute >= startTime.minute)) &&
  //       (currentTime.hour < endTime.hour || (currentTime.hour == endTime.hour && currentTime.minute <= endTime.minute));
  //
  //   emit(state.copyWith(
  //     isWithinTimeFrame: isWithinTimeFrame
  //   ));
  // }

  /// đoạn này để tránh trường hợp người dùng nhập nhanh -> check enable button
  void onTextChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      onChangeAmount(value);
    });
  }

  Future<void> onChangeAmount(String value) async {
    num amount = num.tryParse(value.replaceAll(',', '')) ?? 0;
    String message = '';

    if (value.isNullOrEmpty) {
      emit(state.copyWith(message: message));
      clearData();
      checkButton();
      return;
    }

    if (state.isPayment &&
        amount > (state.balanceCanBeTransfer?.avldepositccp ?? 0)) {
      message = VpUtilityLocalize.current.exceededMaximumBalance;
      emit(state.copyWith(message: message));
      if (!isFocused) {
        await getPositionQuantity(
          symbolFiSmallest: state.contractQuoteBySmallest?.symbol ?? '',
          amount: amount.toInt(),
        );
      }
      return;
    } else if (!state.isPayment &&
        amount > (state.balanceCanBeWithdraw?.avlwithdrawccp ?? 0)) {
      message = VpUtilityLocalize.current.exceededMaximumBalance;
    } else if (!state.isPayment && state.maxAmountWithdraw < amount) {
      message = VpUtilityLocalize.current.transactionAmountExceeded;
    } else if (amount <= feeDepositAndWithdraw) {
      state.isPayment
          ? message = VpUtilityLocalize.current.depositAmountIsGreaterThan5500(
            NumberFormat('#,###').format(feeDepositAndWithdraw),
          )
          : message = VpUtilityLocalize.current.withdrawAmountIsGreaterThan5500(
            NumberFormat('#,###').format(feeDepositAndWithdraw),
          );
      emit(state.copyWith(message: message));
      if (!isFocused) {
        await getPositionQuantity(
          symbolFiSmallest: state.contractQuoteBySmallest?.symbol ?? '',
          amount: amount.toInt(),
        );
      }
      return;
    } else if (state.isPayment &&
        amount > (state.balanceCanBeTransfer?.avldepositccp ?? 0)) {
      message = VpUtilityLocalize.current.exceededMaximumBalance;
      emit(state.copyWith(message: message));
      if (!isFocused) {
        await getPositionQuantity(
          symbolFiSmallest: state.contractQuoteBySmallest?.symbol ?? '',
          amount: amount.toInt(),
        );
      }
      return;
    } else if (!state.isPayment &&
        amount > (state.balanceCanBeWithdraw?.avlwithdrawccp ?? 0)) {
      message = VpUtilityLocalize.current.exceededMaximumBalance;
      emit(state.copyWith(message: message));
      await getPositionQuantity(
        symbolFiSmallest: state.contractQuoteBySmallest?.symbol ?? '',
        amount: amount.toInt(),
      );
      return;
    } else if (state.isPayment && !isFocused) {
      await getPositionQuantity(
        symbolFiSmallest: state.contractQuoteBySmallest?.symbol ?? '',
        amount: amount.toInt(),
      );
    }
    emit(state.copyWith(message: message));
    await checkButton();
  }

  Future<void> checkButton() async {
    someContracts.selection = TextSelection.fromPosition(
      TextPosition(offset: someContracts.text.length),
    );
    String? someContract = checkAndParse(someContracts.text);
    if (someContract != null) {
      emit(
        state.copyWith(
          contentTrans:
              state.isPayment
                  ? textContentTransferController.text
                  : textContentWithdrawController.text,
          amountOfMoney: moneyController.text,
          someContracts: someContract,
        ),
      );
    } else {
      emit(
        state.copyWith(
          contentTrans:
              state.isPayment
                  ? textContentTransferController.text
                  : textContentWithdrawController.text,
          amountOfMoney: moneyController.text,
          someContracts: null,
        ),
      );
    }
  }

  String? checkAndParse(String input) {
    if (input.isNotEmpty) {
      try {
        if (int.parse(input) >= 0) {
          return input;
        }
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  Future<void> intData() async {
    clearData();
    await getSystemConfigs();
    getBalanceCanBeTransfer();
    getBalanceCanBeWithdraw();
    getContractQuotes();
    emit(
      state.copyWith(
        switchValueDeposit: account.isAutoDeposit == 'Y' ? true : false,
        switchValueWithdraw: account.isAutoWithdraw == 'Y' ? true : false,
      ),
    );
  }

  void clearData() {
    textContentTransferController.text = 'Nop tien ky quy phai sinh';
    textContentWithdrawController.text = 'Rut tien ky quy phai sinh';
    moneyController.clear();
    someContracts.clear();
    emit(state.copyWith(message: '', someContracts: null, amountOfMoney: null));
  }

  void changeTransactionType(bool valueType) {
    emit(state.copyWith(transactionType: valueType));
    moneyController.clear();
    someContracts.clear();
    onChangeAmount(moneyController.text);
    intData();
    debugPrint('---------->>>>> $valueType');
  }

  Future<void> getBalanceCanBeTransfer() async {
    emit(state.copyWith(isVPBankLoading: true));

    try {
      final result = await _repository.getBalanceCanBeTransfer(
        accountId: account.id!,
      );

      emit(
        state.copyWith(
          balanceCanBeTransfer: result.model,
          isVPBankLoading: false,
        ),
      );
    } catch (e) {
      emit(state.copyWith(isVPBankLoading: false));
      showError(e);
    }
  }

  Future<void> getBalanceCanBeWithdraw() async {
    emit(state.copyWith(isVPBankLoading: true));
    try {
      final result = await _repository.getBalanceCanBeWithdraw(
        accountId: account.id!,
      );
      emit(
        state.copyWith(
          balanceCanBeWithdraw: result.model,
          isVPBankLoading: false,
        ),
      );
    } catch (e) {
      emit(state.copyWith(isVPBankLoading: false));
      showError(e);
    }
  }

  /// lấy danh sách hợp đồng tương lai để lọc ra hợp đồng (symbol)
  /// có cficode = FI và ngày đáo hạn nhỏ nhất
  Future<List<ContractQuoteModel>> getContractQuotes() async {
    try {
      final result = await _repository.getContractQuotes();
      if (result.isSuccess) {
        final validateContract = _getContractQuotesAfterUpdateKRXLogic(
          result.model,
        );
        handleGetContractQuoteSuccess(listContract: validateContract);
      }
    } catch (e) {
      showError(e);
    }
    return [];
  }

  List<ContractQuoteModel> _getContractQuotesAfterUpdateKRXLogic(
    List<ContractQuoteModel> data,
  ) {
    return data.where((element) => element.isCS == "N").toList();
  }

  Future<List<ContractQuoteModel>> handleGetContractQuoteSuccess({
    required List<ContractQuoteModel> listContract,
  }) async {
    // Convert currDate to DateTime
    String? convertedCurrentDate = _convertDateFormat(
      dateString: state.currDate,
    );
    DateTime currentDate =
        convertedCurrentDate != null
            ? DateTime.parse(convertedCurrentDate)
            : DateTime.now();

    // Filter models with cficode = "FI" and delisteddt > currDate
    List<ContractQuoteModel> filteredModels =
        listContract.where((model) {
          String? convertedDelisteddt = _convertDateFormat(
            dateString: model.delisteddt,
          );
          if (convertedDelisteddt != null) {
            DateTime delisteddt = DateTime.parse(convertedDelisteddt);
            return model.cficode == 'FI' && delisteddt.isAfter(currentDate);
          }
          return false;
        }).toList();

    // If there are no models after filtering, return the original list
    if (filteredModels.isEmpty) {
      return listContract;
    }

    // Find the model with the smallest delisteddt
    ContractQuoteModel smallestDelistedModel = filteredModels.reduce((a, b) {
      String? convertedDelisteddtA = _convertDateFormat(
        dateString: a.delisteddt,
      );
      String? convertedDelisteddtB = _convertDateFormat(
        dateString: b.delisteddt,
      );
      DateTime delisteddtA =
          convertedDelisteddtA != null
              ? DateTime.parse(convertedDelisteddtA)
              : DateTime.now();
      DateTime delisteddtB =
          convertedDelisteddtB != null
              ? DateTime.parse(convertedDelisteddtB)
              : DateTime.now();
      return delisteddtA.isBefore(delisteddtB) ? a : b;
    });
    // Emit the state with the smallest delisteddt model
    emit(state.copyWith(contractQuoteBySmallest: smallestDelistedModel));
    // debugLog(
    //     "symbol contractQuoteBySmallest: ${state.contractQuoteBySmallest?.symbol}");
    return listContract;
  }

  Future<void> getDerivativeAccount() async {
    emit(state.copyWith(isVPBankLoading: true));
    try {
      final data = await mediator.send(GetSubAccountQuery<SubAccountModel?>());

      account = data!;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      showError(e);
    } finally {
      emit(state.copyWith(isVPBankLoading: false));
    }
  }

  /// tự động đăng ký nộp / rút tiền
  Future<void> autoRegisterDepositOrWithdraw({
    required String auto,
    required bool isDeposit,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isVPBankLoading: true));

      final action = isDeposit ? "DEPOSIT" : "WITHDRAW";

      final result = await _repository
          .autoRegisterDepositOrWithdraw(
            custodycd: custodyCd,
            action: action,
            auto: auto,
            xCustId: custId,
          )
          .whenComplete(() async {
            await getDerivativeAccount();

            emit(
              state.copyWith(
                switchValueDeposit: account.isAutoDeposit == 'Y',
                switchValueWithdraw: account.isAutoWithdraw == 'Y',
              ),
            );

            debugPrint("switchValueDeposit:  ${account.isAutoDeposit}");
            debugPrint("switchValueWithdraw:  ${account.isAutoWithdraw}");
          });

      emit(state.copyWith(isVPBankLoading: false));
      debugPrint("result:  ${result.response.getResponseCode()}");

      showSnackBar(
        context,
        result.response.isSuccess()
            ? VpUtilityLocalize.current.savedSuccessfully
            : "${VpUtilityLocalize.current.saveFailedDueTo} ${result.response.getResponseMessage()}",
        isSuccess: result.response.isSuccess(),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      showError(e);
    }
  }

  Future<void> getMarginAmountAndPositionQuantity({
    required String symbolFiSmallest,
    required int amount,
  }) async {
    emit(state.copyWith(isVPBankLoading: true));

    try {
      final result = await _repository.getMarginAmountAndPositionQuantity(
        accountId: account.id!,
        symbol: symbolFiSmallest,
        amount: amount,
        calType: "CIM",
      );
      emit(
        state.copyWith(
          marginAmountAndPositionQuantity: result.model,
          isVPBankLoading: false,
        ),
      );

      final formatter = NumberFormat("#,###");
      moneyController.text = formatter
          .format(state.marginAmountAndPositionQuantity?.amount ?? 0)
          .toString()
          .replaceAll(".", ",");
      // someContracts.text = state.marginAmountAndPositionQuantity?.qtty.toString() ?? '';
      onChangeAmount(moneyController.text);
      // checkButton();
      // debugPrint("Data:  ${state.balanceCanBeTransfer?.avldepositccp}");
    } catch (e) {
      showError(e);
    }
  }

  Future<void> getPositionQuantity({
    required String symbolFiSmallest,
    required int amount,
  }) async {
    emit(state.copyWith(isVPBankLoading: true));

    try {
      final result = await _repository.getMarginAmountAndPositionQuantity(
        accountId: account.id!,
        symbol: symbolFiSmallest,
        amount: amount,
        calType: "CQTTY",
      );
      emit(
        state.copyWith(
          marginAmountAndPositionQuantity: result.model,
          isVPBankLoading: false,
        ),
      );
      someContracts.text =
          state.marginAmountAndPositionQuantity?.qtty.toString() ?? '';
      checkButton();
    } catch (e) {
      showError(e);
    }
  }

  Future<void> handleOnPressed({
    required BuildContext context,
    required TransactionBloc bloc,
  }) async {
    await checkStatusTransByTime();

    final isAutoWithdraw = account.isAutoWithdraw;
    final isAutoDeposit = account.isAutoDeposit;
    final isPayment = state.isPayment;
    final amount = int.parse(state.amountOfMoney.replaceAll(",", ""));
    final desc =
        isPayment
            ? textContentTransferController.text
            : textContentWithdrawController.text;

    final depositIMCash = state.statusTransByTime?.depositIMCash;
    final withdrawImCash = state.statusTransByTime?.withdrawImCash;

    if (isPayment == true) {
      /// nop
      if (depositIMCash == 'Y') {
        transferMoney(
          context: context,
          amount: amount,
          iofee: "IFEE",
          desc: desc,
        );
      } else {
        showTimeTransactionWarning(
          context,
          state.outTimeDepositStartTime ?? '',
          state.outTimeDepositEndTime ?? '',
          true,
        );
      }
    } else {
      if (withdrawImCash == 'Y') {
        if (isAutoDeposit == "Y" && !isPayment) {
          showNoteDerivativeDialog(
            context: context,
            bloc: bloc,
            startTime: state.tooltipStartTimeAutoDeposit ?? '',
            endTime: state.tooltipEndTimeAutoDeposit ?? '',
          );
        } else {
          transferMoney(
            context: context,
            amount: amount,
            iofee: "IFEE",
            desc: desc,
          );
        }
      } else {
        showTimeTransactionWarning(
          context,
          state.outTimeWithdrawStartTime ?? '',
          state.outTimeWithdrawEndTime ?? '',
          false,
        );
      }
    }
  }

  Future<void> transferMoney({
    required String iofee,
    required int amount,
    required String desc,
    required BuildContext context,
  }) async {
    Future<bool> Function({
      required String iofee,
      required int amount,
      required BuildContext context,
    })
    checkFunction;
    Future<bool> Function({
      required String iofee,
      required int amount,
      required String desc,
    })
    actionFunction;
    String successRoute;
    String failRoute;

    if (state.isPayment) {
      // NỘP TIỀN
      checkFunction = checkDeposit;
      actionFunction = deposit;
      successRoute = VpUtilityRouter.derivativeTransactionSuccess.routeName;
      failRoute = VpUtilityRouter.derivativeTransactionFail.routeName;
    } else {
      // RÚT TIỀN
      checkFunction = checkWithdraw;
      actionFunction = withdraw;
      successRoute = VpUtilityRouter.derivativeWithdrawSuccess.routeName;
      failRoute = VpUtilityRouter.derivativeWithdrawFail.routeName;
    }

    final dataCheck = await checkFunction(
      iofee: iofee,
      amount: amount,
      context: context,
    );
    if (dataCheck) {
      final dataAction = await actionFunction(
        iofee: iofee,
        amount: amount,
        desc: desc,
      );
      final route = dataAction ? successRoute : failRoute;

      if (notInputSmartOTP != null && notInputSmartOTP == true) {
        return;
      } else if (isSmartOTPUnRegistered != null &&
          isSmartOTPUnRegistered == false) {
        context.pushNamed(
          route,
          queryParameters:
              DataCheckDepositOrWithdraw(
                amount: amount,
                iofee: iofee,
                desc: desc,
                actualMoney: moneyController.text,
                fee: feeDepositAndWithdraw,
              ).toQueryParams(),
        );
      }

      if (dataAction) {
        clearData();
        intData();
        checkButton();
      }
    }
  }

  /// [NỘP] - Check thực hiện giao dịch nộp tiền CCP
  Future<bool> checkDeposit({
    required String iofee,
    required int amount,
    required BuildContext context,
  }) async {
    emit(state.copyWith(isVPBankLoading: true));
    try {
      final result = await _repository.checkDeposit(
        accountId: account.id!,
        dataCheck: DataCheckDepositOrWithdraw(amount: amount, iofee: iofee),
      );

      final isSuccess = result.isSuccess;
      emit(
        state.copyWith(
          isVPBankLoading: false,
          checkDepositOrWithdraw: isSuccess ? result.model : null,
        ),
      );

      if (isSuccess) {
        debugPrint('checkDeposit: ${state.checkDepositOrWithdraw?.fee}');
        return true;
      } else {
        showSnackBar(
          context,
          result.response.getResponseMessage(),
          isSuccess: result.response.isSuccess(),
        );
        return false;
      }
    } catch (e) {
      emit(state.copyWith(isVPBankLoading: false));
      showError(e);
      return false;
    }
  }

  /// [NỘP] - Check thực hiện giao dịch nộp tiền CCP
  Future<bool> deposit({
    required String iofee,
    required int amount,
    required String desc,
  }) async {
    emit(state.copyWith(isVPBankLoading: true));
    try {
      final result = await _repository.depositMoney(
        xCustId: custId,
        xRequestId: DateTime.now().millisecondsSinceEpoch.toString(),
        accountId: account.id!,
        dataCheck: DataCheckDepositOrWithdraw(
          amount: amount,
          iofee: iofee,
          desc: desc,
        ),
      );
      emit(state.copyWith(isVPBankLoading: false));
      isSmartOTPUnRegistered = result.response.isSmartOTPUnRegistered();
      notInputSmartOTP = result.response.notInputSmartOTP();

      if (result.isSuccess) {
        return true;
      } else if (notInputSmartOTP == false) {
        showErrorMessage(result.message);
        return false;
      }

      return false;
    } catch (e) {
      emit(state.copyWith(isVPBankLoading: false));
      showError(e);
      return false;
    }
  }

  /// [RÚT] - Check thực hiện giao dịch rút tiền CCP
  Future<bool> checkWithdraw({
    required String iofee,
    required int amount,
    required BuildContext context,
  }) async {
    emit(state.copyWith(isVPBankLoading: true));
    try {
      final result = await _repository.checkWithdraw(
        accountId: account.id!,
        dataCheck: DataCheckDepositOrWithdraw(amount: amount, iofee: iofee),
      );

      emit(
        state.copyWith(
          isVPBankLoading: false,
          checkDepositOrWithdraw: result.isSuccess ? result.model : null,
        ),
      );

      if (result.isSuccess) {
        return true;
      } else {
        showSnackBar(
          context,
          result.response.getResponseMessage(),
          isSuccess: result.response.isSuccess(),
        );
        return false;
      }
    } catch (e) {
      emit(state.copyWith(isVPBankLoading: false));
      showError(e);
      return false;
    }
  }

  /// [RÚT] - Thực hiện giao dịch rút tiền CCP
  Future<bool> withdraw({
    required String iofee,
    required int amount,
    required String desc,
  }) async {
    emit(state.copyWith(isVPBankLoading: true));
    try {
      final result = await _repository.withdrawMoney(
        xCustId: custId,
        xRequestId: DateTime.now().millisecondsSinceEpoch.toString(),
        accountId: account.id!,
        dataCheck: DataCheckDepositOrWithdraw(
          amount: amount,
          iofee: iofee,
          desc: desc,
        ),
      );
      emit(state.copyWith(isVPBankLoading: false));
      isSmartOTPUnRegistered = result.response.isSmartOTPUnRegistered();
      notInputSmartOTP = result.response.notInputSmartOTP();
      if (result.isSuccess) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      emit(state.copyWith(isVPBankLoading: false));
      showError(e);
      return false;
    }
  }

  Future<void> onFieldChanged({required String value}) async {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      if (value == '') {
        clearData();
      }
      if (value != '') {
        getMarginAmountAndPositionQuantity(
          symbolFiSmallest: state.contractQuoteBySmallest?.symbol ?? '',
          amount: int.parse(value),
        );
      }
      checkButton();
    });
  }

  Future<void> getSystemConfigs() async {
    final systemConfigs = await getListSystemConfig();
    final systemConfigList = getSystemConfigList(systemConfigs);
    emit(state.copyWith(systemConfigList: systemConfigList));
    getStartTimeAndEndTime();
  }

  Future<List<SysConfigModel>> getListSystemConfig() async {
    try {
      emit(state.copyWith(isVPBankLoading: true));
      final result = await _repository.getSystemConfig();
      if (result.isSuccess) {
        emit(state.copyWith(isVPBankLoading: false));
        return result.model;
      }
    } catch (e) {
      emit(state.copyWith(isVPBankLoading: false));
      showError(e);
    }
    return [];
  }

  List<SysConfigModel> getSystemConfigList(List<SysConfigModel> systemConfigs) {
    final systemConfigList =
        List.generate(
          systemConfigs.length,
          (index) =>
              SysConfigModel.fromSystemConfigEntity(systemConfigs[index]),
        ).toList();
    return systemConfigList;
  }

  void getStartTimeAndEndTime() {
    /// lấy thời gian nộp tiền text lưu ý
    final onlineDepositStartTime = getConfigValue(
      ONLINE_DEPOSIT_START_TIME,
      state.systemConfigList ?? [],
    );
    final onlineDepositEndTime = getConfigValue(
      ONLINE_DEPOSIT_END_TIME,
      state.systemConfigList ?? [],
    );

    /// lấy thời gian rút tiền text lưu ý
    final onlineWithdrawStartTime = getConfigValue(
      ONLINE_WITHDRAW_START_TIME,
      state.systemConfigList ?? [],
    );
    final onlineWithdrawEndTime = getConfigValue(
      ONLINE_WITHDRAW_END_TIME,
      state.systemConfigList ?? [],
    );

    /// lấy thời gian của tooltip tự động nộp
    final depositAutoBeginTime = getConfigValue(
      DEPOSIT_AUTO_BEGIN_TIME,
      state.systemConfigList ?? [],
    );
    final depositAutoEndTime = getConfigValue(
      DEPOSIT_AUTO_END_TIME,
      state.systemConfigList ?? [],
    );

    /// lấy thời gian của tooltip tự động rút
    final withdrawAutoBeginTime = getConfigValue(
      WITHDRAW_AUTO_BEGIN_TIME,
      state.systemConfigList ?? [],
    );
    final withdrawAutoEndTime = getConfigValue(
      WITHDRAW_AUTO_END_TIME,
      state.systemConfigList ?? [],
    );

    /// lấy thời gian của dialog ngoài giờ giao dịch nộp
    final outTimeDepositStartTime = getConfigValue(
      ONLINE_DEPOSIT_START_TIME,
      state.systemConfigList ?? [],
    );
    final outTimeDepositEndTime = getConfigValue(
      ONLINE_DEPOSIT_END_TIME,
      state.systemConfigList ?? [],
    );

    /// lấy thời gian của dialog ngoài giờ giao dịch rút
    final outTimeWithdrawStartTime = getConfigValue(
      ONLINE_WITHDRAW_START_TIME,
      state.systemConfigList ?? [],
    );
    final outTimeWithdrawEndTime = getConfigValue(
      ONLINE_WITHDRAW_END_TIME,
      state.systemConfigList ?? [],
    );

    /// lấy số tiền tối đa có thể rút
    final maxAmountWithdraw = getConfigValue(
      CASH_WDLMT_PER_TRAN,
      state.systemConfigList ?? [],
    );

    /// lấy ngày hệ thống hiện tại
    final currentDate = getConfigValue(CURRDATE, state.systemConfigList ?? []);

    /// lấy Phí nộp/rút ký quỹ
    feeDepositAndWithdraw =
        num.tryParse(
          getConfigValue(
                FEE_DEPOSIT_AND_WITHDRAW,
                state.systemConfigList ?? [],
              ) ??
              '0',
        ) ??
        0;

    /// text lưu ý nộp
    final formattedStartTime =
        onlineDepositStartTime != null
            ? formatTimeH(time: onlineDepositStartTime)
            : 'N/A';
    final formattedEndTime =
        onlineDepositEndTime != null
            ? formatTimeH(time: onlineDepositEndTime)
            : 'N/A';

    /// text lưu ý rút
    final formattedStartTimeWithdraw =
        onlineWithdrawStartTime != null
            ? formatTimeH(time: onlineWithdrawStartTime)
            : 'N/A';
    final formattedEndTimeWithdraw =
        onlineWithdrawEndTime != null
            ? formatTimeH(time: onlineWithdrawEndTime)
            : 'N/A';

    /// tooltip nộp
    final formattedAutoBeginTime =
        depositAutoBeginTime != null
            ? formatTimeTwoDot(time: depositAutoBeginTime)
            : 'N/A';
    final formattedAutoEndTime =
        depositAutoEndTime != null
            ? formatTimeTwoDot(time: depositAutoEndTime)
            : 'N/A';

    /// tooltip rút
    final formattedAutoBeginTimeWithdraw =
        withdrawAutoBeginTime != null
            ? formatTimeTwoDot(time: withdrawAutoBeginTime)
            : 'N/A';
    final formattedAutoEndTimeWithdraw =
        withdrawAutoEndTime != null
            ? formatTimeTwoDot(time: withdrawAutoEndTime)
            : 'N/A';

    /// lấy thời gian của dialog ngoài giờ giao dịch nộp
    final formattedOutTimeDepositStartTime =
        outTimeDepositStartTime != null
            ? formatTimeTwoDot(time: outTimeDepositStartTime)
            : 'N/A';
    final formattedOutTimeDepositEndTime =
        outTimeDepositEndTime != null
            ? formatTimeTwoDot(time: outTimeDepositEndTime)
            : 'N/A';

    /// lấy thời gian của dialog ngoài giờ giao dịch rút
    final formattedOutTimeWithdrawStartTime =
        outTimeWithdrawStartTime != null
            ? formatTimeTwoDot(time: outTimeWithdrawStartTime)
            : 'N/A';
    final formattedOutTimeWithdrawEndTime =
        outTimeWithdrawEndTime != null
            ? formatTimeTwoDot(time: outTimeWithdrawEndTime)
            : 'N/A';

    /// lấy ngày hệ thống hiện tại
    final currDate =
        currentDate != null ? convertDateFormat(date: currentDate) : 'N/A';

    emit(
      state.copyWith(
        onlineDepositStartTime: formattedStartTime,
        onlineDepositEndTime: formattedEndTime,
        onlineWithdrawStartTime: formattedStartTimeWithdraw,
        onlineWithdrawEndTime: formattedEndTimeWithdraw,
        tooltipStartTimeAutoDeposit: formattedAutoBeginTime,
        tooltipEndTimeAutoDeposit: formattedAutoEndTime,
        tooltipStartTimeAutoWithdraw: formattedAutoBeginTimeWithdraw,
        tooltipEndTimeAutoWithdraw: formattedAutoEndTimeWithdraw,
        outTimeDepositStartTime: formattedOutTimeDepositStartTime,
        outTimeDepositEndTime: formattedOutTimeDepositEndTime,
        outTimeWithdrawStartTime: formattedOutTimeWithdrawStartTime,
        outTimeWithdrawEndTime: formattedOutTimeWithdrawEndTime,
        maxAmountWithdraw: num.tryParse(maxAmountWithdraw ?? ''),
        currDate: currDate,
        feeDepositAndWithdraw: feeDepositAndWithdraw,
      ),
    );
  }

  Future<void> checkStatusTransByTime() async {
    emit(state.copyWith(isVPBankLoading: true));

    try {
      final result = await _repository.checkStatusTransByTime(
        dateAndTime: AppTimeUtils.getDateTimeStringFull(),
      );

      emit(
        state.copyWith(statusTransByTime: result.model, isVPBankLoading: false),
      );
    } catch (e) {
      emit(state.copyWith(isVPBankLoading: false));
      showError(e);
    }
  }

  // void processDataTransaction(SocketData socketData) {
  //   // debugLog("TRANSACTION: ${socketData.toJson()}");
  //   intData();
  // }
  //
  // @override
  // void updateDataFromSocket(SocketData data) {
  //   if (data.e == ISocketEvent.DT.eventNameBySocket) {
  //     processDataTransaction(data);
  //   }
  // }
}

String? getConfigValue(String key, List<SysConfigModel> configList) {
  return configList.firstWhere((element) => element.cfgkey == key).cfgvalue;
}

String formatTimeH({String? time}) {
  if (time != null && time.isNotEmpty) {
    final parts = time.split(':');
    return '${parts[0]}h${parts[1]}';
  } else {
    return '--';
  }
}

String formatTimeTwoDot({String? time}) {
  if (time != null && time.isNotEmpty) {
    final parts = time.split(':');
    return '${parts[0]}:${parts[1]}';
  } else {
    return '--';
  }
}

String? _convertDateFormat({String? dateString}) {
  if (dateString != null && dateString.isNotEmpty) {
    try {
      // Split the date string by "-"
      List<String> parts = dateString.split('-');

      // Check if the parts have exactly 3 components
      if (parts.length == 3) {
        // Ensure the components are of valid lengths for "dd-MM-yyyy"
        if (parts[0].length == 2 &&
            parts[1].length == 2 &&
            parts[2].length == 4) {
          return '${parts[2]}-${parts[1]}-${parts[0]}';
        } else {
          return null;
        }
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  } else {
    return null;
  }
}

String convertDateFormat({String? date}) {
  DateTime dateTime;

  if (date == null || date.isEmpty) {
    dateTime = DateTime.now();
  } else {
    try {
      DateFormat inputFormat = DateFormat('dd/MM/yyyy');
      dateTime = inputFormat.parseStrict(date);
    } catch (e) {
      return '--';
    }
  }

  DateFormat outputFormat = DateFormat('dd-MM-yyyy');
  return outputFormat.format(dateTime);
}
