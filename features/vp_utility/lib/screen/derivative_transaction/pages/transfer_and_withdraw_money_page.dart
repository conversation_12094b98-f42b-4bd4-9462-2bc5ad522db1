import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/derivative_transaction/bloc/transaction_bloc.dart';
import 'package:vp_utility/screen/derivative_transaction/pages/app_text_field.dart';
import 'package:vp_utility/screen/derivative_transaction/pages/widgets/transaction_auto_withdraw_or_deposit.dart';
import 'package:vp_utility/widget/bottom_sheet_tooltip_widget.dart';
import 'package:vp_utility/widget/button/button_widget.dart';
import 'package:vp_utility/widget/derivative_web_view.dart';
import '../bloc/transaction_state.dart';
import '../widget/actual_amount_paid_widget.dart';
import '../widget/derivative_input_transactions.dart';
import '../widget/derivative_manager_header_view.dart';

import '../widget/derivative_spinner_widget.dart';
import '../widget/selected_transaction_type.dart';
import 'readmore_custom.dart';

class TransactionPage extends StatefulWidget {
  const TransactionPage({required this.args, super.key});

  final DerivativeTransactionArgs args;

  @override
  State<TransactionPage> createState() => _TransactionPageState();
}

class _TransactionPageState extends State<TransactionPage> {
  late TransactionBloc _bloc;

  // final String bottomSheetContent = "Số dư tiền trên TKPS bao gồm tiền nộp, nhận tiền thanh toán lãi VM...(không bao gồm tiền chờ rút ký quỹ từ VSD) sẽ được tự động chuyển lên TKKQ (VSD) khi số dư tiền lớn hơn Phí quản lý tài sản tối thiểu (hiện tại là 100,000 đ) và tổng giá trị phí thuế chưa thanh toán tại VPBankS trong khoảng thời gian từ 8:00 đến 14:30 các ngày giao dịch.";
  // final String bottomSheetContentContract = "Là số hợp đồng VN30F1M có thể giao dịch tối đa dự kiến được tính dựa trên số tiền ký quỹ mà Qúy khách muốn nộp lên CCP (đã tính theo biểu phí, thuế hiện hành).\nHoặc Quý khách có thể nhập số lượng HĐ cần giao dịch để hệ thống tính toán ra số tiền cần nộp tương ứng.";
  final String linkHDSDWithdraw =
      "https://hdsd.vpbanks.com.vn/phai-sinh/giao-dich-tien-phai-sinh/rut-tien-tu-tk-ky-quy-tai-ccp";
  final String linkHDSDDeposit =
      "https://hdsd.vpbanks.com.vn/phai-sinh/giao-dich-tien-phai-sinh/nop-tien-vao-tk-ky-quy-tai-ccp";

  DerivativeTransactionArgs get args => widget.args;

  @override
  void initState() {
    super.initState();
    _bloc = TransactionBloc(
      account: args.account,
      custId: args.custId,
      custodyCd: args.custodyCd,
    );
    _bloc.intData();
    _bloc.focusNode.addListener(() {
      _bloc.onFocusChange();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => _bloc,
      child: AppScaffold(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            DerivativeManagerHeaderView(
              subTitle2: VpUtilityLocalize.current.moneyTransactions,
            ),
            BlocBuilder<TransactionBloc, TransactionSate>(
              bloc: _bloc,
              // buildWhen: (p, c) => p != c,
              builder: (context, state) {
                return Expanded(
                  child: Stack(
                    children: [
                      GestureDetector(
                        onTap: () {
                          FocusScope.of(context).unfocus();
                        },
                        child: Column(
                          children: [
                            Expanded(
                              child: SingleChildScrollView(
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      DerivativeSpinnerWidget(
                                        spinnerHeight: 40.0,
                                        title:
                                            VpUtilityLocalize
                                                .current
                                                .transactionType,
                                        content:
                                            state.isPayment == true
                                                ? VpUtilityLocalize
                                                    .current
                                                    .depositCCP
                                                : VpUtilityLocalize
                                                    .current
                                                    .withdrawCCP,
                                        tap: () {
                                          showSelectTransactionTypeBottomSheet(
                                            transactionBloc: _bloc,
                                            context: context,
                                            transactionType: [
                                              VpUtilityLocalize
                                                  .current
                                                  .depositCCP,
                                              VpUtilityLocalize
                                                  .current
                                                  .withdrawCCP,
                                            ],
                                          );
                                        },
                                      ),
                                      const SizedBox(height: 16),
                                      DerivativeSpinnerWidget(
                                        showIcon: false,
                                        spinnerHeight: 40.0,
                                        title:
                                            state.isPayment == true
                                                ? VpUtilityLocalize
                                                    .current
                                                    .transFrom
                                                : VpUtilityLocalize
                                                    .current
                                                    .transTo,
                                        content:
                                            VpUtilityLocalize
                                                .current
                                                .derivativeSubAccount,
                                        tap: () {},
                                      ),
                                      const SizedBox(height: 36),
                                      Text(
                                        VpUtilityLocalize
                                                .current
                                                .amountOfMoney ??
                                            '',
                                        style: vpTextStyle.subtitle14?.copyWith(
                                          color: themeData.black,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Stack(
                                        children: [
                                          AppTextField(
                                            autofocus: false,
                                            // paddingBottom: 0,
                                            // enable: state.receive != null,
                                            typeInput: TextInputType.number,
                                            hintText: '0',
                                            maxLength: 19,
                                            isAllowMoveCursor: false,
                                            textController:
                                                _bloc.moneyController,
                                            isSuccess: state.message.isEmpty,
                                            onChanged:
                                                (value) =>
                                                    _bloc.onTextChanged(value),
                                            // onEditingComplete: _bloc.onEditingComplete(),
                                            messsage:
                                                state.message.isNotEmpty
                                                    ? state.message
                                                    : null,
                                            listFormatter: [
                                              CurrencyInputFormatter(),
                                              FilteringTextInputFormatter
                                                  .digitsOnly,
                                              FirstDigitNonZeroInputFormatter(),
                                              InputNumberFormatter(),
                                            ],
                                            // focusNode: _focus,
                                          ),
                                          Positioned(
                                            right: 20,
                                            top: 8,
                                            child: Text(
                                              'đ',
                                              style: vpTextStyle.body14
                                                  ?.copyWith(
                                                    color: themeData.gray500,
                                                  ),
                                            ),
                                          ),
                                        ],
                                      ),

                                      Visibility(
                                        visible: state.message.isEmpty,
                                        child: Padding(
                                          padding: const EdgeInsets.only(
                                            left: 0.0,
                                          ),
                                          child: Text(
                                            '${VpUtilityLocalize.current.maxMoney}: ${state.isPayment ? (state.balanceCanBeTransfer?.avldepositccp ?? 0).toMoney(symbol: 'đ') : (state.balanceCanBeWithdraw?.avlwithdrawccp ?? 0).toMoney(symbol: 'đ')}',
                                            style: vpTextStyle.captionRegular
                                                ?.copyWith(
                                                  color: themeData.gray900,
                                                ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      //TODO: Hide calculation sum of contracts
                                      // const TransactionCalculationSumOfContracts(),
                                      DerivativeInputTransactions(
                                        style: vpTextStyle.body14?.copyWith(
                                          color: ColorDefine.gray500Dark,
                                        ),
                                        // enabledTextFormField: false,
                                        focusColor: Colors.transparent,
                                        cursorColor: Colors.transparent,
                                        readOnly: true,
                                        showCursor: false,
                                        ctrl:
                                            state.isPayment == true
                                                ? _bloc
                                                    .textContentTransferController
                                                : _bloc
                                                    .textContentWithdrawController,
                                        inputFormatters: [
                                          FilteringTextInputFormatter.allow(
                                            RegExp(r'[a-zA-Z0-9\sÀ-ỹà-ỹ]'),
                                          ),
                                        ],
                                        inputHeight: 40,
                                        title: "Nội dung",
                                        hintText: "",
                                        textSuffixIcon: "",
                                        keyboardType: TextInputType.text,
                                        onFieldChanged: (value) {
                                          _bloc.checkButton();
                                        },
                                      ),
                                      const SizedBox(height: 36),

                                      /// Đợi confirm xem data phía BE trả ra những gì
                                      Container(
                                        decoration: const BoxDecoration(
                                          color: Color(0x29FEC200),
                                          borderRadius: BorderRadius.all(
                                            Radius.circular(8.0),
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 8,
                                          ),
                                          child: ReadMoreTextCustom(
                                            textNote(
                                              isDeposit: state.isPayment,
                                              startTime:
                                                  state
                                                      .onlineDepositStartTime ??
                                                  '',
                                              endTime:
                                                  state.onlineDepositEndTime ??
                                                  '',
                                              startTimeWithdraw:
                                                  state
                                                      .onlineWithdrawStartTime ??
                                                  '',
                                              endTimeWithdraw:
                                                  state.onlineWithdrawEndTime ??
                                                  '',
                                            ),
                                            trimLines: 3,
                                            colorClickableText:
                                                themeData.primary,
                                            trimMode: TrimMode.Line,
                                            trimCollapsedText:
                                                VpUtilityLocalize
                                                    .current
                                                    .seeMore,
                                            // delimiterStyle: TextStyle(color: Colors.red),
                                            trimExpandedText:
                                                VpUtilityLocalize.current.here,
                                            moreStyle: vpTextStyle.body14
                                                ?.copyWith(
                                                  color: themeData.primary,
                                                ),
                                            style: vpTextStyle.body14?.copyWith(
                                              color: vpColor.textPrimary,
                                            ),
                                            // delimiter: "... ",
                                            callback: (data) {
                                              if (data == true) {
                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder:
                                                        (
                                                          context,
                                                        ) => DerivativeWebView(
                                                          url:
                                                              state.isPayment
                                                                  ? linkHDSDDeposit
                                                                  : linkHDSDWithdraw,
                                                        ),
                                                  ),
                                                );
                                              }
                                            },
                                          ),
                                        ),
                                      ),
                                      state.isPayment == true
                                          ? ActualAmountPaidWidget(
                                            money: _bloc.moneyController.text,
                                            fee: state.feeDepositAndWithdraw,
                                          )
                                          : const SizedBox(),
                                      const SizedBox(height: 16),
                                      const TransactionAutoWithdrawOrDeposit(),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            // TextButton(
                            //     onPressed: () {
                            //       // showTimeTransactionWarning(context);
                            //       // showNotiOrderDialog(
                            //       //   context: context,
                            //       // );
                            //     },
                            //     child: Text("Dialog")),
                            const DividerWidget(),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 16,
                                horizontal: 8,
                              ),
                              child: ButtonWidget(
                                action:
                                    '${state.isPayment == true ? VpUtilityLocalize.current.depositDeposit : VpUtilityLocalize.current.withdraw} ',
                                enable:
                                    state.isPayment
                                        ? state.checkEnable
                                        : state.checkEnableButtonWithdraw,
                                onPressed: () async {
                                  _bloc.handleOnPressed(
                                    context: context,
                                    bloc: _bloc,
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      Positioned.fill(
                        child: Visibility(
                          visible: state.isVPBankLoading,
                          child: buildLoadingView(),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  void _onShowTooltip({
    required BuildContext context,
    String? bottomSheetContent,
  }) {
    showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return AppBottomSheet(
          child: TooltipBottomSheet(bottomSheetContent: bottomSheetContent),
        );
      },
    );
  }

  Widget buildLoadingView() {
    return ColoredBox(
      color: isDark ? Colors.transparent : themeData.gray900.withOpacity(0.2),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: themeData.bgPopup,
            borderRadius: BorderRadius.circular(16),
          ),
          child: const VPBankLoading(),
        ),
      ),
    );
  }

  String tooltipAutoTransfer({
    required bool isDeposit,
    required String startTimeDeposit,
    required String endTimeDeposit,
    required String startTimeWithdraw,
    required String endTimeWithdraw,
  }) {
    if (isDeposit == true) {
      return '${VpUtilityLocalize.current.tooltipAutoTransferDeposit1} $startTimeDeposit ${VpUtilityLocalize.current.to} $endTimeDeposit ${VpUtilityLocalize.current.tooltipAutoTransferDeposit2}';
    } else {
      return '${VpUtilityLocalize.current.tooltipAutoTransferWithdraw1} $startTimeWithdraw ${VpUtilityLocalize.current.to} $endTimeWithdraw ${VpUtilityLocalize.current.tooltipAutoTransferWithdraw2}';
    }
  }

  String textNote({
    required bool isDeposit,
    required String startTime,
    required String endTime,
    required String startTimeWithdraw,
    required String endTimeWithdraw,
  }) {
    if (isDeposit == true) {
      return '${VpUtilityLocalize.current.textNoteInTransferScreenDeposit1} $startTime ${VpUtilityLocalize.current.to} $endTime ${VpUtilityLocalize.current.textNoteInTransferScreenDeposit2}';
    } else {
      return '${VpUtilityLocalize.current.textNoteInTransferScreenWithdraw1} $startTimeWithdraw ${VpUtilityLocalize.current.to} $endTimeWithdraw ${VpUtilityLocalize.current.textNoteInTransferScreenWithdraw2}';
    }
  }
}

class FirstDigitNonZeroInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Kiểm tra nếu giá trị ban đầu chưa có và người dùng nhập số 0
    if (oldValue.text.isEmpty && newValue.text == '0') {
      return oldValue;
    }
    // Thay thế số 0 nếu nó là ký tự đầu tiên và người dùng nhập ký tự khác
    if (oldValue.text == '0' && newValue.text.isNotEmpty) {
      final String newText = newValue.text.replaceFirst('0', '');
      return TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    }
    return newValue;
  }
}
