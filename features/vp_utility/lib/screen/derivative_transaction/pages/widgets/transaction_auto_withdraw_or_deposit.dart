import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/derivative_transaction/bloc/transaction_bloc.dart';
import 'package:vp_utility/screen/derivative_transaction/bloc/transaction_state.dart';
import 'package:vp_utility/screen/derivative_transaction/pages/widgets/transaction_transfer_and_withdraw_money_mixin.dart';

class TransactionAutoWithdrawOrDeposit extends StatelessWidget
    with TransactionTransferAndWithdrawMoneyMixin {
  const TransactionAutoWithdrawOrDeposit({super.key});

  String tooltipAutoTransfer({
    required bool isDeposit,
    required String startTimeDeposit,
    required String endTimeDeposit,
    required String startTimeWithdraw,
    required String endTimeWithdraw,
  }) {
    if (isDeposit == true) {
      return '${VpUtilityLocalize.current.tooltipAutoTransferDeposit1} $startTimeDeposit ${VpUtilityLocalize.current.to} $endTimeDeposit ${VpUtilityLocalize.current.tooltipAutoTransferDeposit2}';
    } else {
      return '${VpUtilityLocalize.current.tooltipAutoTransferWithdraw1} $startTimeWithdraw ${VpUtilityLocalize.current.to} $endTimeWithdraw ${VpUtilityLocalize.current.tooltipAutoTransferWithdraw2}';
    }
  }

  String _getTitle(bool isPayment) {
    if (isPayment) {
      return VpUtilityLocalize.current.autoDeposit;
    }
    return VpUtilityLocalize.current.autoWithdraw;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TransactionBloc, TransactionSate>(
      builder: (context, state) {
        final isPayment = state.isPayment;
        // TODO Hide when deposit in derivative krx
        if (isPayment) {
          return SizedBox.shrink();
        } else {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    _getTitle(state.isPayment),
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      onShowTooltip(
                        context: context,
                        bottomSheetContent: tooltipAutoTransfer(
                          isDeposit: state.isPayment,
                          startTimeDeposit:
                              state.tooltipStartTimeAutoDeposit ?? '',
                          endTimeDeposit: state.tooltipEndTimeAutoDeposit ?? '',
                          startTimeWithdraw:
                              state.tooltipStartTimeAutoWithdraw ?? '',
                          endTimeWithdraw:
                              state.tooltipEndTimeAutoWithdraw ?? '',
                        ),
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(left: 8, top: 2),
                      child: VpUtilityAssets.icons.icIToolTip.svg(
                        height: 12,
                        width: 12,
                      ),
                    ),
                  ),
                ],
              ),
              Container(
                decoration: const BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x29000000),
                      // Màu đen với opacity 29 (0x29)
                      offset: Offset(0, 4),
                      // Di chuyển theo trục Y (dy = 4)
                      blurRadius: 12, // Mờ với bán kính 12
                    ),
                  ],
                ),
                child: FlutterSwitch(
                  width: 51.0,
                  height: 32.0,
                  // valueFontSize: 0.0,
                  toggleSize: 28.0,
                  value:
                      state.isPayment == true
                          ? state.switchValueDeposit
                          : state.switchValueWithdraw,
                  borderRadius: 99.0,
                  activeColor: themeData.primaryDark,
                  inactiveColor:
                      isDark ? themeData.buttonDisableBg : themeData.gray500,
                  inactiveToggleColor:
                      isDark ? themeData.gray500 : themeData.white,
                  padding: 2.0,
                  onToggle: (val) {
                    // setState(() {
                    //   switchValue = val;
                    // });
                    context
                        .read<TransactionBloc>()
                        .autoRegisterDepositOrWithdraw(
                          auto: val == true ? "Y" : "N",
                          isDeposit: state.isPayment,
                          context: context,
                        );
                  },
                ),
              ),
            ],
          );
        }
      },
    );
  }
}
