import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart' show MultipleLocalizations;
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_utility/core/repository/advance_payment/advance_repository.dart';
import 'package:vp_utility/core/repository/advance_payment/advance_repository_impl.dart';
import 'package:vp_utility/core/repository/derivative_statement/derivative_statement_repository.dart';
import 'package:vp_utility/core/repository/derivative_statement/derivative_statement_repository_impl.dart';
import 'package:vp_utility/core/repository/emonie/e_invest_repository.dart';
import 'package:vp_utility/core/repository/emonie/e_invest_repository_impl.dart';
import 'package:vp_utility/core/repository/financial_service/financial_service_impl.dart';
import 'package:vp_utility/core/repository/financial_service/financial_service_repository.dart';
import 'package:vp_utility/core/repository/model_porfolio/model_portfolio_repository.dart';
import 'package:vp_utility/core/repository/model_porfolio/model_portfolio_repository_impl.dart';
import 'package:vp_utility/core/repository/order_confirm/order_confirm_repository.dart';
import 'package:vp_utility/core/repository/order_confirm/order_confirm_repository_impl.dart';
import 'package:vp_utility/core/repository/ranking/ranking_repository.dart';
import 'package:vp_utility/core/repository/ranking/ranking_repository_impl.dart';
import 'package:vp_utility/core/repository/recommendation_repository.dart';
import 'package:vp_utility/core/repository/stock_filter/stock_filter_repository.dart';
import 'package:vp_utility/core/repository/stock_filter/stock_filter_repository_impl.dart';
import 'package:vp_utility/core/repository/utility_repository.dart';
import 'package:vp_utility/core/service/recommendation_service.dart';
import 'package:vp_utility/core/service/utility_service.dart';
import 'package:vp_utility/cubit/emonie/details_emonie/details_emonie_cubit.dart';
import 'package:vp_utility/cubit/emonie/list_emonie/list_emonie_cubit.dart';
import 'package:vp_utility/cubit/emonie/register_emonie/register_emonie_cubit.dart';
import 'package:vp_utility/cubit/emonie/status_emonie/status_emonie_cubit.dart';
import 'package:vp_utility/cubit/order_confirm/bloc/order_confirm_bloc.dart';
import 'package:vp_utility/generated/intl/messages_all.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/derivative/derivative_income_statement_args.dart';
import 'package:vp_utility/model/request/stock_filter/stock_filter_param.dart';
import 'package:vp_utility/model/response/advance_payment/param/advance_success_param.dart';
import 'package:vp_utility/model/response/advance_payment/param/register_advance_payment_param.dart';
import 'package:vp_utility/model/response/emonie/e_invest_model_2.dart';
import 'package:vp_utility/model/response/financial_service/ln_se_rate_entity.dart';
import 'package:vp_utility/model/response/model_porfolio/model_portfolio_model.dart';
import 'package:vp_utility/router/vp_utility_router.dart';
import 'package:vp_utility/screen/advance_payment/ui/advance_payment_page.dart';
import 'package:vp_utility/screen/advance_payment/ui/advance_successful_page.dart';
import 'package:vp_utility/screen/advance_payment/ui/register_one_time_page.dart';
import 'package:vp_utility/screen/derivative_income_statement/data/repository/income_statement_repository_impl.dart';
import 'package:vp_utility/screen/derivative_income_statement/data/service/derivative_income_statement_service.dart';
import 'package:vp_utility/screen/derivative_statement/derivative_statement_page.dart';
import 'package:vp_utility/screen/derivative_transaction/data/models/data_check_deposit_or_withdraw.dart';
import 'package:vp_utility/screen/derivative_transaction/data/repository/transaction_repository_impl.dart';
import 'package:vp_utility/screen/derivative_transaction/data/service/derivative_transaction_service.dart';
import 'package:vp_utility/screen/derivative_transaction/pages/transfer_and_withdraw_money_page.dart';
import 'package:vp_utility/screen/derivative_transaction/widget/derivative_transfer_fail_screen.dart';
import 'package:vp_utility/screen/derivative_transaction/widget/derivative_transfer_success_screen.dart';
import 'package:vp_utility/screen/derivative_transaction/widget/derivative_withdraw_fail_screen.dart';
import 'package:vp_utility/screen/derivative_transaction/widget/derivative_withdraw_success_screen.dart';
import 'package:vp_utility/screen/derivative_transaction_history/data/repository/transaction_history_repository_impl.dart';
import 'package:vp_utility/screen/derivative_transaction_history/data/service/derivative_transaction_history_service.dart';
import 'package:vp_utility/screen/derivative_transaction_history/pages/transaction_history_page.dart';
import 'package:vp_utility/screen/emonie/list_emonie/list_emonie_page.dart';
import 'package:vp_utility/screen/emonie/list_emonie/tooltip_emonie_page.dart';
import 'package:vp_utility/screen/emonie/register_emonie/register_emonie_page.dart';
import 'package:vp_utility/screen/emonie/status_emonie/status_emonie_page.dart';
import 'package:vp_utility/screen/event/event_page.dart';
import 'package:vp_utility/screen/financial_service/ui/financial_service_package_page.dart';
import 'package:vp_utility/screen/financial_service/ui/register_package/register_package_page.dart';
import 'package:vp_utility/screen/model_portfolio/ui/model_portfolio_contract_page.dart';
import 'package:vp_utility/screen/model_portfolio/ui/model_portfolio_page.dart';
import 'package:vp_utility/screen/order_confirm/ui/order_confirm_page.dart';
import 'package:vp_utility/screen/profit_lost_history/profit_lost_history_page.dart';
import 'package:vp_utility/screen/ranking/ranking_page.dart';
import 'package:vp_utility/screen/recommendation_home/enum/recommendation_type.dart';
import 'package:vp_utility/screen/recommendation_list/recommendation_list.dart';
import 'package:vp_utility/screen/securities_statement/securities_statement_page.dart';
import 'package:vp_utility/screen/stock_filter/create_stock_filtter/create_stock_filter_page_new.dart';
import 'package:vp_utility/screen/stock_right/register_stock_right_page.dart';

import 'core/repository/event/event_repository.dart';
import 'core/repository/event/event_repository_impl.dart';
import 'core/repository/stock_right/right_off_repository.dart';
import 'core/repository/stock_right/right_off_repository_impl.dart';
import 'core/repository/stock_transfer/stock_transfer_repository.dart';
import 'core/repository/stock_transfer/stock_transfer_repository_impl.dart';
import 'model/response/stock_right/right_off_model.dart';
import 'screen/derivative_income_statement/pages/income_statement_page.dart';
import 'screen/emonie/details_emonie/details_emonie_page.dart';
import 'screen/stock_filter/page/stock_filter_page.dart';
import 'screen/stock_right/stock_right_page.dart';
import 'screen/stock_transfer/ui/stock_transfer_page.dart';

class VpUtilityModule implements Module {
  @override
  void injectServices(GetIt service) {
    service.registerFactory<DerivativeStatementRepository>(
      () => DerivativeStatementRepositoryImpl(restClient: service()),
    );
    service.registerLazySingleton(() => UtilityService(service()));
    service.registerFactory<UtilityRepository>(
      () => UtilityRepositoryImpl(service(), utilityService: service()),
    );
    service.registerFactory<OrderConfirmRepository>(
      () => OrderConfirmRepositoryImpl(restClient: service()),
    );
    service.registerFactory<AdvanceRepository>(
      () => AdvanceRepositoryImpl(restClient: service()),
    );
    service.registerFactory<ModelPortfolioRepository>(
      () => ModelPortfolioRepositoryImpl(restClient: service()),
    );
    service.registerFactory<RankingRepository>(
      () => RankingRepositoryImpl(restClient: service()),
    );
    service.registerLazySingleton(
      () => RecommendationService(
        service(),
        // todo: sửa lại endpoint
        baseUrl: "https://neopro-uat.vpbanks.com.vn",
      ),
    );
    service.registerLazySingleton<RecommendationRepository>(
      () => RecommendationRepositoryImpl(recommendationService: service()),
    );
    service.registerLazySingleton<FinancialServiceRepository>(
      () => FinancialServicePackageImpl(
        restClient: service(),
        fssRestClient: service(),
        investDio: service(),
      ),
    );
    service.registerLazySingleton<EventRepository>(
      () => EventRepositoryImpl(restClient: service()),
    );
    service.registerLazySingleton<RightOffRepository>(
      () => RightOffRepositoryImpl(restClient: service()),
    );
    service.registerLazySingleton<StockTransferRepository>(
      () => StockTransferRepositoryImpl(restClient: service()),
    );
    service.registerLazySingleton<EInvestRepository>(
      () => EInvestRepositoryImpl(restClient: service()),
    );

    service.registerLazySingleton<DerivativeIncomeStatementService>(
      () => DerivativeIncomeStatementService(service()),
    );

    service.registerLazySingleton<DerivativeIncomeStatementRepository>(
      () => IncomeStatementRepositoryImpl(service: service()),
    );
    service.registerLazySingleton<StockFilterRepository>(
      () => StockFilterRepositoryImpl(restClient: service()),
    );

    service.registerLazySingleton<DerivativeTransactionService>(
      () => DerivativeTransactionService(service()),
    );

    service.registerLazySingleton<DerivativeTransactionRepository>(
      () => DerivativeTransactionRepositoryImpl(service: service()),
    );
    service.registerLazySingleton<DerivativeTransactionHistoryService>(
      () => DerivativeTransactionHistoryService(service()),
    );

    service.registerLazySingleton<DerivativeTransactionHistoryRepository>(
      () => DerivativeTransactionHistoryRepositoryImpl(service: service()),
    );
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: VpUtilityRouter.recommendationList.routeName,
        name: VpUtilityRouter.recommendationList.routeName,
        builder:
            (context, state) => RecommendationList(
              recommendationType: state.extra as RecommendationType?,
            ),
      ),
      GoRoute(
        path: VpUtilityRouter.securitiesStatement.routeName,
        name: VpUtilityRouter.securitiesStatement.routeName,
        builder: (context, state) => SecuritiesStatementPage(),
      ),
      GoRoute(
        path: VpUtilityRouter.orderConfirm.routeName,
        name: VpUtilityRouter.orderConfirm.routeName,
        builder: (context, state) {
          return MultiBlocProvider(
            providers: [
              BlocProvider(create: (_) => OrderConfirmBloc()),
              // BlocProvider.value(
              //   value: GetIt.instance.get<HomeOrderConfirmCubit>(),
              // ),
            ],
            child: const OrderConfirmPage(),
          );
        },
      ),
      GoRoute(
        path: VpUtilityRouter.advancePayment.routeName,
        name: VpUtilityRouter.advancePayment.routeName,
        builder: (context, state) {
          return const AdvancePaymentPage();
        },
      ),
      GoRoute(
        path: VpUtilityRouter.advancePaymentSuccess.routeName,
        name: VpUtilityRouter.advancePaymentSuccess.routeName,
        builder: (context, state) {
          final AdvanceSuccessParam param = state.extra as AdvanceSuccessParam;
          return AdvanceSuccessfulPage(param: param);
        },
      ),
      GoRoute(
        path: VpUtilityRouter.registerAdvancePayment.routeName,
        name: VpUtilityRouter.registerAdvancePayment.routeName,
        builder: (context, state) {
          final RegisterAdvancePaymentParam param =
              state.extra as RegisterAdvancePaymentParam;
          return RegisterOneTimePage(param: param);
        },
      ),
      GoRoute(
        path: VpUtilityRouter.modelPortfolio.routeName,
        name: VpUtilityRouter.modelPortfolio.routeName,
        builder: (context, state) {
          return const ModelPortfolioPage();
        },
      ),
      GoRoute(
        path: VpUtilityRouter.modelPortfolioContract.routeName,
        name: VpUtilityRouter.modelPortfolioContract.routeName,
        builder: (context, state) {
          final modelPortfolio = state.extra as ModelPortfolioModel;
          return ModelPortfolioContractPage(contract: modelPortfolio);
        },
      ),
      GoRoute(
        path: VpUtilityRouter.ranking.routeName,
        name: VpUtilityRouter.ranking.routeName,
        builder: (context, state) {
          return const RankingPage();
        },
      ),
      GoRoute(
        path: VpUtilityRouter.financialServicePackage.routeName,
        name: VpUtilityRouter.financialServicePackage.routeName,
        builder: (context, state) {
          return FinancialServicePackagePage();
        },
      ),
      GoRoute(
        path: VpUtilityRouter.registerPackage.routeName,
        name: VpUtilityRouter.registerPackage.routeName,
        builder: (context, state) {
          return RegisterPackagePage(data: state.extra as LnSeRateEntity);
        },
      ),
      GoRoute(
        path: VpUtilityRouter.event.routeName,
        name: VpUtilityRouter.event.routeName,
        builder: (context, state) {
          return EventPage();
        },
      ),
      GoRoute(
        path: VpUtilityRouter.registerRight.routeName,
        name: VpUtilityRouter.registerRight.routeName,
        builder: (context, state) {
          return RegisterStockRightPage(model: state.extra as RightOffModel);
        },
      ),
      GoRoute(
        path: VpUtilityRouter.stockRight.routeName,
        name: VpUtilityRouter.stockRight.routeName,
        builder: (context, state) {
          return StockRightPage();
        },
      ),
      GoRoute(
        path: VpUtilityRouter.stockTransfer.routeName,
        name: VpUtilityRouter.stockTransfer.routeName,
        builder: (context, state) {
          return StockTransferPage();
        },
      ),
      GoRoute(
        path: VpUtilityRouter.listEmonie.routeName,
        name: VpUtilityRouter.listEmonie.routeName,
        builder: (context, state) {
          return BlocProvider(
            create: (context) => ListEmonieCubit(),
            child: const ListEmoniePage(),
          );
        },
      ),
      GoRoute(
        path: VpUtilityRouter.registerEmonie.routeName,
        name: VpUtilityRouter.registerEmonie.routeName,
        builder: (context, state) {
          return BlocProvider(
            create: (context) => RegisterEmonieCubit(),
            child: const RegisterEmoniePage(),
          );
        },
      ),
      GoRoute(
        path: VpUtilityRouter.statusEmonie.routeName,
        name: VpUtilityRouter.statusEmonie.routeName,
        builder: (context, state) {
          return BlocProvider(
            create: (context) => StatusEmonieCubit(),
            child: const StatusEmoniePage(),
          );
        },
      ),
      GoRoute(
        path: VpUtilityRouter.tooltipEmonie.routeName,
        name: VpUtilityRouter.tooltipEmonie.routeName,
        builder: (context, state) {
          return TooltipEmoniePage();
        },
      ),
      GoRoute(
        path: VpUtilityRouter.detailsEmonie.routeName,
        name: VpUtilityRouter.detailsEmonie.routeName,
        builder: (context, state) {
          return BlocProvider(
            create:
                (context) => DetailsEmonieCubit(
                  emonieModel: state.extra as EInvestModel2,
                ),
            child: const DetailsEmoniePage(),
          );
        },
      ),
      GoRoute(
        path: VpUtilityRouter.derivativeStatement.routeName,
        name: VpUtilityRouter.derivativeStatement.routeName,
        builder: (context, state) {
          return const DerivativeStatementPage();
        },
      ),
      GoRoute(
        path: VpUtilityRouter.profitHistory.routeName,
        name: VpUtilityRouter.profitHistory.routeName,
        builder: (context, state) {
          return const ProfitLostHistoryPage();
        },
      ),
      GoRoute(
        path: VpUtilityRouter.derivativeIncomeStatement.routeName,
        name: VpUtilityRouter.derivativeIncomeStatement.routeName,
        builder: (context, state) {
          final args = DerivativeIncomeStatementArgsExtensions.fromQueryParams(
            state.uri.queryParameters,
          );

          return DerivativeIncomeStatementPage(accountId: args.accountId);
        },
      ),
      GoRoute(
        path: VpUtilityRouter.createStockFilter.routeName,
        name: VpUtilityRouter.createStockFilter.routeName,
        builder:
            (context, state) => CreateStockFilterPageNew(
              param: state.extra as StockFilterParam,
            ),
      ),
      GoRoute(
        path: VpUtilityRouter.stockFilter.routeName,
        name: VpUtilityRouter.stockFilter.routeName,
        builder: (context, state) => StockFilterPage(),
      ),
      GoRoute(
        path: VpUtilityRouter.derivativeTransaction.routeName,
        name: VpUtilityRouter.derivativeTransaction.routeName,
        builder: (context, state) {
          return TransactionPage(
            args: DerivativeTransactionArgsExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          );
        },
      ),
      GoRoute(
        path: VpUtilityRouter.derivativeTransactionSuccess.routeName,
        name: VpUtilityRouter.derivativeTransactionSuccess.routeName,
        builder: (context, state) {
          return DerivativeTransferSuccessScreen(
            param: DataCheckDepositOrWithdrawExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          );
        },
      ),
      GoRoute(
        path: VpUtilityRouter.derivativeWithdrawFail.routeName,
        name: VpUtilityRouter.derivativeWithdrawFail.routeName,
        builder: (context, state) {
          return DerivativeWithdrawFailScreen(
            param: DataCheckDepositOrWithdrawExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          );
        },
      ),

      GoRoute(
        path: VpUtilityRouter.derivativeWithdrawSuccess.routeName,
        name: VpUtilityRouter.derivativeWithdrawSuccess.routeName,
        builder: (context, state) {
          return DerivativeWithdrawSuccessScreen(
            param: DataCheckDepositOrWithdrawExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          );
        },
      ),
      GoRoute(
        path: VpUtilityRouter.derivativeTransactionHistory.routeName,
        name: VpUtilityRouter.derivativeTransactionHistory.routeName,
        builder: (context, state) {
          return TransactionHistoryPage(
            subAccount: SubAccountModelExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          );
        },
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return "vpUtility";
  }
}

class MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = MultiLocalizationsDelegate();

  @override
  Future<VpUtilityLocalize> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => VpUtilityLocalize.load(locale),
      setDefaultLocale: true,
    );
  }
}
