import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:vp_auth/core/repository/account_repository.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

class MediatorRegistry {
  final accountRepository = GetIt.instance<AccountRepository>();

  void register() {
    mediator.register<GetSubAccountQuery<SubAccountModel?>, SubAccountModel?>(
      (_) => _onAccountChangeListener(),
    );
  }

  Future<SubAccountModel?> _onAccountChangeListener() async {
    try {
      final accountList = await accountRepository.getAccountList();

      GetIt.instance<SubAccountCubit>().setSubAccounts(accountList);

      return GetIt.instance<SubAccountCubit>().derivativeActiveAccount;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return null;
  }
}
