import 'dart:async';

import 'package:vp_common/event_bus/mediator_query.dart';

final mediator = Mediator();

typedef ErasedHandler = FutureOr<dynamic> Function(Object);

class Mediator {
  final Map<Type, ErasedHandler> _handlers = {};

  void register<Q extends Query<R>, R>(FutureOr<R> Function(Q) h) {
    _handlers[Q] = (Object req) => h(req as Q);
  }

  Future<R> send<R>(Query<R> req) async {
    final f = _handlers[req.runtimeType];

    if (f == null) {
      throw StateError('No handler for ${req.runtimeType}');
    }

    final result = await f(req);
    return result as R;
  }
}
