import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:vp_socket/base/lifecycle_connectivity_bridge.dart';
import 'package:vp_socket/data/enum/socket_event.dart';
import 'package:vp_socket/base/base_socket.dart';
import 'package:vp_socket/socket_impl/account/account_sub.dart';

class VPSocketAccountConnect extends VPBaseSocket
    with SocketLifecycleConnectivityMixin {
  @override
  String get path => '/trading-stream/broker';

  @override
  String get baseUrl => 'wss://neopro-uat.vpbanks.com.vn';

  SocketLifecycleConnectivityObserver? _bridge;

  final _controller = StreamController<Map<String, dynamic>>.broadcast();

  late Stream<Map<String, dynamic>> stream = _controller.stream;

  @override
  Future<void> connect() {
    _bridge ??= SocketLifecycleConnectivityObserver(this)..start();

    return super.connect();
  }

  @override
  void onConnected() {
    super.onConnected();

    send(VPAccountAuthSub(channel: 'auth', token: token!).subscribeData);
  }

  @override
  void transformData(dynamic rawData) {
    try {
      final data = rawData is Map ? rawData : jsonDecode(rawData);

      emitEventSafety(SocketEvent.messageIn, payload: data);

      if (data is! Map) return;

      if (data['code'] == 401) {
        closeNoRetry();

        return;
      }

      _controller.add(data.cast<String, dynamic>());
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  @override
  Future<void> dispose() {
    _controller.close();
    _bridge?.stop();
    return super.dispose();
  }

  @override
  bool get authenticationRequired => true;

  @override
  set token(String? token) {
    super.token = token;
  }

  @override
  bool Function()? get canRetryPredicate => () => isReconnectAllowed;

  @override
  void connectNow() => reconnect();

  @override
  Future<void> closeNoRetry() => close();
}
