import 'dart:convert';

import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

part "derivative_transaction_args.g.dart";

@GoRouterParams()
class DerivativeTransactionArgs {
  const DerivativeTransactionArgs({
    required this.account,
    required this.custId,
    required this.custodyCd,
  });

  final SubAccountModel account;

  final String custId;

  final String custodyCd;
}
