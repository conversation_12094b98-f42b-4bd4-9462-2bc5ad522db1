// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'derivative_transaction_args.dart';

// **************************************************************************
// FromQueryParamsGenerator
// **************************************************************************

extension DerivativeTransactionArgsExtensions on DerivativeTransactionArgs {
  static DerivativeTransactionArgs fromQueryParams(Map<String, String> params) {
    return DerivativeTransactionArgs(
      account: SubAccountModel.fromJson(jsonDecode(params['account']!)),
      custId: params['custId']!,
      custodyCd: params['custodyCd']!,
    );
  }

  Map<String, String> toQueryParams() {
    return {
      'account': jsonEncode(account.toJson()),
      'custId': custId.toString(),
      'custodyCd': custodyCd.toString(),
    };
  }
}
