import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

typedef PercentChangeStyleBuilder =
    TextStyle? Function(num? percent, num? closePrice, Color? color);

typedef PercentChangePriceBuilder =
    String? Function(num? percent, num? closePrice);

class VPPercentChangeItemView extends StatelessWidget {
  const VPPercentChangeItemView({
    required this.symbol,
    this.percentBuilder,
    this.styleBuilder,
    this.initClosePrice,
    this.initPercentChange,
    this.initTextColor,
    this.onTap,
    this.undefineWhenClosePriceNull = true,
    super.key,
  });

  factory VPPercentChangeItemView.stock({
    required StockInfoModel stock,
    Function(String?)? onTap,
    num? initClosePrice,
    PriceChangeStyleBuilder? styleBuilder,
    PriceChangePriceBuilder? percentBuilder,
    bool undefineWhenClosePriceNull = true,
  }) => VPPercentChangeItemView(
    symbol: stock.symbol,
    initClosePrice: stock.closePrice,
    initPercentChange: stock.percentPriceChange,
    initTextColor: stock.color,
    styleBuilder: styleBuilder,
    percentBuilder: percentBuilder,
    onTap: onTap,
    undefineWhenClosePriceNull: undefineWhenClosePriceNull,
  );

  final String symbol;

  final num? initPercentChange;

  final num? initClosePrice;

  final Color? initTextColor;

  final bool undefineWhenClosePriceNull;

  final Function(String?)? onTap;

  final PercentChangeStyleBuilder? styleBuilder;

  final PercentChangePriceBuilder? percentBuilder;

  @override
  Widget build(BuildContext context) {
    return VPSocketInvestmentBuilder<VPStockInfoData>(
      symbol: symbol,
      channel: VPSocketChannel.stockInfo.name,
      buildWhen: (preData, data) {
        final oldClosePrice = preData?.closePrice ?? initClosePrice;

        final oldPercent = preData?.percentChange ?? initPercentChange;

        return oldPercent != data?.percentChange ||
            oldClosePrice != data?.closePrice;
      },
      builder: (_, __, data, child) {
        final closePrice = data?.closePrice?.toDouble() ?? initClosePrice;

        final percent = data?.percentChange ?? initPercentChange;

        final color = data.color ?? initTextColor ?? vpColor.textTertiary;

        final percentFormatted = FormatUtils.formatPercent(
          percent,
          showPositiveSign: false,
        );

        return Text(
          undefineWhenClosePriceNull
              ? closePrice == null || percentFormatted == null
                  ? '-%'
                  : percentBuilder?.call(percent, closePrice) ??
                      ' ($percentFormatted)'
              : percentBuilder?.call(percent, closePrice) ??
                  ' ($percentFormatted)',
          textAlign: TextAlign.end,
          style:
              styleBuilder?.call(percent, closePrice, color) ??
              vpTextStyle.subtitle14.copyColor(color),
        );
      },
    );
  }
}
