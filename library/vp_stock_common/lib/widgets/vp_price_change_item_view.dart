import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

typedef PriceChangeStyleBuilder =
    TextStyle? Function(num? change, num? closePrice, Color? color);

typedef PriceChangePriceBuilder =
    String? Function(num? change, num? closePrice);

class VPPriceChangeItemView extends StatelessWidget {
  VPPriceChangeItemView({
    required this.symbol,
    this.initClosePrice,
    this.initPriceChange,
    this.initTextColor,
    this.styleBuilder,
    this.priceBuilder,
    this.onTap,
    this.textAlign = TextAlign.end,
    this.alignment,
    this.undefineWhenClosePriceNull = true,
    super.key,
  });

  factory VPPriceChangeItemView.stock({
    required StockInfoModel stock,
    Function(String?)? onTap,
    num? initClosePrice,
    PriceChangeStyleBuilder? styleBuilder,
    PriceChangePriceBuilder? changeBuilder,
    bool undefineWhenClosePriceNull = true,
  }) => VPPriceChangeItemView(
    symbol: stock.symbol,
    initClosePrice: stock.closePrice,
    initPriceChange: stock.priceChange,
    initTextColor: stock.color,
    styleBuilder: styleBuilder,
    priceBuilder: changeBuilder,
    onTap: onTap,
    undefineWhenClosePriceNull: undefineWhenClosePriceNull,
  );

  final String symbol;

  final num? initPriceChange;

  final num? initClosePrice;

  final Color? initTextColor;

  final Function(String?)? onTap;

  final TextAlign? textAlign;

  final AlignmentGeometry? alignment;

  final PriceChangeStyleBuilder? styleBuilder;

  final PriceChangePriceBuilder? priceBuilder;

  final bool undefineWhenClosePriceNull;

  @override
  Widget build(BuildContext context) {
    return VPSocketInvestmentBuilder<VPStockInfoData>(
      symbol: symbol,
      channel: VPSocketChannel.stockInfo.name,
      buildWhen: (preData, data) {
        final oldClosePrice = preData?.closePrice ?? initClosePrice;

        final oldPriceChange = preData?.priceChange ?? initPriceChange;

        return oldPriceChange != data?.priceChange ||
            oldClosePrice != data?.closePrice;
      },
      builder: (_, __, data, child) {
        final closePrice = data?.closePrice?.toDouble() ?? initClosePrice;

        final change = data?.priceChange ?? initPriceChange;

        final changeValue = FormatUtils.formatClosePrice(
          change,
          showSign: false,
        );

        final color = data.color ?? initTextColor ?? vpColor.textTertiary;

        return Text(
          undefineWhenClosePriceNull
              ? closePrice == null
                  ? '-'
                  : priceBuilder?.call(change, closePrice) ?? changeValue ?? '-'
              : priceBuilder?.call(change, closePrice) ?? changeValue ?? '-',
          style:
              styleBuilder?.call(change, closePrice, color) ??
              vpTextStyle.subtitle14.copyColor(color),
          textAlign: TextAlign.end,
        );
      },
    );
  }
}
